[{"C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\authContext.tsx": "3", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\playerContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\hostContext.tsx": "5", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\timeListenerContext.tsx": "6", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\soundContext.tsx": "7", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\routes\\ProtectedRoute.tsx": "8", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.tsx": "9", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\FallBack.tsx": "10", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Spectator\\SpectatorJoin.tsx": "11", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.tsx": "12", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\JoinRoom\\JoinRoom.tsx": "13", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Login\\Login.tsx": "14", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.tsx": "15", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.tsx": "16", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.tsx": "17", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.tsx": "18", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.tsx": "19", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Home\\Home.tsx": "20", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.tsx": "21", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Room\\CreateRoom.tsx": "22", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound3.tsx": "23", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRoundTurn.tsx": "24", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound4.tsx": "25", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound2.tsx": "26", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\Dashboard.tsx": "27", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound1.tsx": "28", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\services.ts": "29", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\service.ts": "30", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\service.ts": "31", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\firebaseServices.ts": "32", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\services.ts": "33", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\auth.service.ts": "34", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.tsx": "35", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useAuth.ts": "36", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\uploadAssestServices.ts": "37", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\room.service.ts": "38", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Footer.tsx": "39", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.tsx": "40", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.tsx": "41", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.tsx": "42", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\UploadTest.tsx": "43", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\SetUpMatch.tsx": "44", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\ViewTest.tsx": "45", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRoundTurn.tsx": "46", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.tsx": "47", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRound4.tsx": "48", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\History.tsx": "49", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Host\\Host.tsx": "50", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRoundTurn.tsx": "51", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRound4.tsx": "52", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\http.ts": "53", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\firebase-config.ts": "54", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.tsx": "55", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Header.tsx": "56", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\services.ts": "57", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\testManagement.service.ts": "58", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\utils.ts": "59", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useListener.ts": "60", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\PlayerAnswerInput.tsx": "61", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\GameGrid.tsx": "62", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.tsx": "63", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.tsx": "64", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostManagement.tsx": "65", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostAnswer.tsx": "66", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\utils\\processFile.utils.ts": "67", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostQuestionPreview.tsx": "68", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\RulesModal.tsx": "69", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostGuideModal.tsx": "70", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerColorSelector.tsx": "71", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\SimpleColorPicker.tsx": "72", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\tokenRefresh.service.ts": "73", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useTokenRefresh.ts": "74", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ErrorBoundary.tsx": "75", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\providers\\ReduxProvider.tsx": "76", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\index.ts": "77", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\authSlice.ts": "78", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\uiSlice.ts": "79", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\roomSlice.ts": "80", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\gameSlice.ts": "81", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.migrated.tsx": "82", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.migrated.tsx": "83", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round4.migrated.tsx": "84", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.migrated.tsx": "85", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.migrated.tsx": "86", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.migrated.tsx": "87", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.migrated.tsx": "88", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.migrated.tsx": "89", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.migrated.tsx": "90", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\index.ts": "91", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\index.ts": "92", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\firebase\\index.ts": "93", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\index.ts": "94", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\index.ts": "95", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\index.ts": "96", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\index.ts": "97", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\firebase\\useFirebaseListener.ts": "98", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useLocalStorage.ts": "99", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useDebounce.ts": "100", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useAsync.ts": "101", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\useAuthApi.ts": "102", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\useGameApi.ts": "103", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\Input.tsx": "104", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\Button.tsx": "105", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\Button.variants.ts": "106", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\Input.variants.ts": "107", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\auth\\tokenService.ts": "108", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\game\\gameApi.ts": "109", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\firebase\\realtime.ts": "110", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\auth\\authApi.ts": "111", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\firebase\\config.ts": "112", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\api\\client.ts": "113", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\index.ts": "114", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\api.constants.ts": "115", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\game.constants.ts": "116", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.migrated.tsx": "117", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.migrated.tsx": "118", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.migrated.tsx": "119", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.migrated.tsx": "120", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\utils\\migration.ts": "121", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.migrated.tsx": "122", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.migrated.tsx": "123", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.migrated.tsx": "124", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.migrated.tsx": "125"}, {"size": 531, "mtime": 1742041483045, "results": "126", "hashOfConfig": "127"}, {"size": 8472, "mtime": 1753320498541, "results": "128", "hashOfConfig": "127"}, {"size": 1945, "mtime": 1751515978853, "results": "129", "hashOfConfig": "127"}, {"size": 3318, "mtime": 1751205902719, "results": "130", "hashOfConfig": "127"}, {"size": 12318, "mtime": 1751815416920, "results": "131", "hashOfConfig": "127"}, {"size": 3007, "mtime": 1750132275454, "results": "132", "hashOfConfig": "127"}, {"size": 2210, "mtime": 1748877596389, "results": "133", "hashOfConfig": "127"}, {"size": 5742, "mtime": 1751515978847, "results": "134", "hashOfConfig": "127"}, {"size": 3063, "mtime": 1750646733694, "results": "135", "hashOfConfig": "127"}, {"size": 10853, "mtime": 1751813331809, "results": "136", "hashOfConfig": "127"}, {"size": 5916, "mtime": 1751468518454, "results": "137", "hashOfConfig": "127"}, {"size": 1091, "mtime": 1750606536117, "results": "138", "hashOfConfig": "127"}, {"size": 8831, "mtime": 1751469082283, "results": "139", "hashOfConfig": "127"}, {"size": 5973, "mtime": 1751467878917, "results": "140", "hashOfConfig": "127"}, {"size": 2428, "mtime": 1748535778291, "results": "141", "hashOfConfig": "127"}, {"size": 2985, "mtime": 1748882058132, "results": "142", "hashOfConfig": "127"}, {"size": 2519, "mtime": 1750907852110, "results": "143", "hashOfConfig": "127"}, {"size": 9153, "mtime": 1752762362793, "results": "144", "hashOfConfig": "127"}, {"size": 3560, "mtime": 1750858199098, "results": "145", "hashOfConfig": "127"}, {"size": 9231, "mtime": 1748535442800, "results": "146", "hashOfConfig": "127"}, {"size": 2434, "mtime": 1748537556126, "results": "147", "hashOfConfig": "127"}, {"size": 2950, "mtime": 1749916604844, "results": "148", "hashOfConfig": "127"}, {"size": 286, "mtime": 1744882291961, "results": "149", "hashOfConfig": "127"}, {"size": 500, "mtime": 1750907175563, "results": "150", "hashOfConfig": "127"}, {"size": 1202, "mtime": 1749697405299, "results": "151", "hashOfConfig": "127"}, {"size": 1350, "mtime": 1747936224953, "results": "152", "hashOfConfig": "127"}, {"size": 6142, "mtime": 1751771488939, "results": "153", "hashOfConfig": "127"}, {"size": 391, "mtime": 1748164720734, "results": "154", "hashOfConfig": "127"}, {"size": 16423, "mtime": 1752829867448, "results": "155", "hashOfConfig": "127"}, {"size": 12288, "mtime": 1752803800875, "results": "156", "hashOfConfig": "127"}, {"size": 4013, "mtime": 1752755496277, "results": "157", "hashOfConfig": "127"}, {"size": 19532, "mtime": 1751705284770, "results": "158", "hashOfConfig": "127"}, {"size": 9582, "mtime": 1752720987966, "results": "159", "hashOfConfig": "127"}, {"size": 1461, "mtime": 1751771152469, "results": "160", "hashOfConfig": "127"}, {"size": 2665, "mtime": 1750606460471, "results": "161", "hashOfConfig": "127"}, {"size": 3650, "mtime": 1749640904327, "results": "162", "hashOfConfig": "127"}, {"size": 1791, "mtime": 1751515978848, "results": "163", "hashOfConfig": "127"}, {"size": 1175, "mtime": 1752727399105, "results": "164", "hashOfConfig": "127"}, {"size": 1170, "mtime": 1741426442370, "results": "165", "hashOfConfig": "127"}, {"size": 9006, "mtime": 1751816803836, "results": "166", "hashOfConfig": "127"}, {"size": 35961, "mtime": 1751815841773, "results": "167", "hashOfConfig": "127"}, {"size": 21763, "mtime": 1751815785464, "results": "168", "hashOfConfig": "127"}, {"size": 4939, "mtime": 1750003466586, "results": "169", "hashOfConfig": "127"}, {"size": 27019, "mtime": 1752740248775, "results": "170", "hashOfConfig": "127"}, {"size": 18850, "mtime": 1752738498726, "results": "171", "hashOfConfig": "127"}, {"size": 8496, "mtime": 1750907101790, "results": "172", "hashOfConfig": "127"}, {"size": 575, "mtime": 1748534874974, "results": "173", "hashOfConfig": "127"}, {"size": 5356, "mtime": 1750860093881, "results": "174", "hashOfConfig": "127"}, {"size": 7200, "mtime": 1750743744045, "results": "175", "hashOfConfig": "127"}, {"size": 524, "mtime": 1748606359769, "results": "176", "hashOfConfig": "127"}, {"size": 8786, "mtime": 1751211350591, "results": "177", "hashOfConfig": "127"}, {"size": 19437, "mtime": 1751783887714, "results": "178", "hashOfConfig": "127"}, {"size": 3477, "mtime": 1752737148040, "results": "179", "hashOfConfig": "127"}, {"size": 787, "mtime": 1748599490140, "results": "180", "hashOfConfig": "127"}, {"size": 5432, "mtime": 1751780054124, "results": "181", "hashOfConfig": "127"}, {"size": 4866, "mtime": 1751714260898, "results": "182", "hashOfConfig": "127"}, {"size": 2464, "mtime": 1752829137284, "results": "183", "hashOfConfig": "127"}, {"size": 968, "mtime": 1750004006914, "results": "184", "hashOfConfig": "127"}, {"size": 11090, "mtime": 1749823808632, "results": "185", "hashOfConfig": "127"}, {"size": 7991, "mtime": 1750859938041, "results": "186", "hashOfConfig": "127"}, {"size": 2132, "mtime": 1751714717852, "results": "187", "hashOfConfig": "127"}, {"size": 6286, "mtime": 1749654059721, "results": "188", "hashOfConfig": "127"}, {"size": 14956, "mtime": 1751815334305, "results": "189", "hashOfConfig": "127"}, {"size": 9062, "mtime": 1751779544380, "results": "190", "hashOfConfig": "127"}, {"size": 18061, "mtime": 1752804447146, "results": "191", "hashOfConfig": "127"}, {"size": 20547, "mtime": 1751709091207, "results": "192", "hashOfConfig": "127"}, {"size": 326, "mtime": 1750003544449, "results": "193", "hashOfConfig": "127"}, {"size": 2678, "mtime": 1751210716770, "results": "194", "hashOfConfig": "127"}, {"size": 8734, "mtime": 1751366308588, "results": "195", "hashOfConfig": "127"}, {"size": 8700, "mtime": 1751716766283, "results": "196", "hashOfConfig": "127"}, {"size": 9219, "mtime": 1751446268973, "results": "197", "hashOfConfig": "127"}, {"size": 3672, "mtime": 1751446814342, "results": "198", "hashOfConfig": "127"}, {"size": 5456, "mtime": 1751471046304, "results": "199", "hashOfConfig": "127"}, {"size": 3507, "mtime": 1751470890921, "results": "200", "hashOfConfig": "127"}, {"size": 1444, "mtime": 1751812774732, "results": "201", "hashOfConfig": "127"}, {"size": 371, "mtime": 1753267395618, "results": "202", "hashOfConfig": "127"}, {"size": 1044, "mtime": 1753267066457, "results": "203", "hashOfConfig": "127"}, {"size": 5691, "mtime": 1753267189603, "results": "204", "hashOfConfig": "127"}, {"size": 6674, "mtime": 1753271972321, "results": "205", "hashOfConfig": "127"}, {"size": 8530, "mtime": 1753267252448, "results": "206", "hashOfConfig": "127"}, {"size": 7791, "mtime": 1753267220917, "results": "207", "hashOfConfig": "127"}, {"size": 17278, "mtime": 1753270843209, "results": "208", "hashOfConfig": "127"}, {"size": 7920, "mtime": 1753269776713, "results": "209", "hashOfConfig": "127"}, {"size": 14633, "mtime": 1753270396031, "results": "210", "hashOfConfig": "127"}, {"size": 4547, "mtime": 1753269879918, "results": "211", "hashOfConfig": "127"}, {"size": 12223, "mtime": 1753271411595, "results": "212", "hashOfConfig": "127"}, {"size": 4671, "mtime": 1753272258539, "results": "213", "hashOfConfig": "127"}, {"size": 6486, "mtime": 1753270448596, "results": "214", "hashOfConfig": "127"}, {"size": 4104, "mtime": 1753269833195, "results": "215", "hashOfConfig": "127"}, {"size": 6043, "mtime": 1753270419856, "results": "216", "hashOfConfig": "127"}, {"size": 101, "mtime": 1753268063327, "results": "217", "hashOfConfig": "127"}, {"size": 257, "mtime": 1753267384855, "results": "218", "hashOfConfig": "127"}, {"size": 197, "mtime": 1753268047281, "results": "219", "hashOfConfig": "127"}, {"size": 183, "mtime": 1753268055528, "results": "220", "hashOfConfig": "127"}, {"size": 248, "mtime": 1753268030106, "results": "221", "hashOfConfig": "127"}, {"size": 178, "mtime": 1753267371651, "results": "222", "hashOfConfig": "127"}, {"size": 187, "mtime": 1753267327382, "results": "223", "hashOfConfig": "127"}, {"size": 6563, "mtime": 1753267972885, "results": "224", "hashOfConfig": "127"}, {"size": 2239, "mtime": 1753268006513, "results": "225", "hashOfConfig": "127"}, {"size": 1139, "mtime": 1753268020220, "results": "226", "hashOfConfig": "127"}, {"size": 2354, "mtime": 1753267989827, "results": "227", "hashOfConfig": "127"}, {"size": 3716, "mtime": 1753267909667, "results": "228", "hashOfConfig": "127"}, {"size": 6115, "mtime": 1753267940461, "results": "229", "hashOfConfig": "127"}, {"size": 2336, "mtime": 1753267363516, "results": "230", "hashOfConfig": "127"}, {"size": 1528, "mtime": 1753267319456, "results": "231", "hashOfConfig": "127"}, {"size": 1775, "mtime": 1753267307155, "results": "232", "hashOfConfig": "127"}, {"size": 1325, "mtime": 1753267349503, "results": "233", "hashOfConfig": "127"}, {"size": 4308, "mtime": 1753267793947, "results": "234", "hashOfConfig": "127"}, {"size": 4134, "mtime": 1753267821629, "results": "235", "hashOfConfig": "127"}, {"size": 6169, "mtime": 1753267886359, "results": "236", "hashOfConfig": "127"}, {"size": 2178, "mtime": 1753267758146, "results": "237", "hashOfConfig": "127"}, {"size": 798, "mtime": 1753268706149, "results": "238", "hashOfConfig": "127"}, {"size": 4415, "mtime": 1753268364172, "results": "239", "hashOfConfig": "127"}, {"size": 103, "mtime": 1753267437502, "results": "240", "hashOfConfig": "127"}, {"size": 1836, "mtime": 1753267413340, "results": "241", "hashOfConfig": "127"}, {"size": 1631, "mtime": 1753267430578, "results": "242", "hashOfConfig": "127"}, {"size": 1351, "mtime": 1753270167497, "results": "243", "hashOfConfig": "127"}, {"size": 8937, "mtime": 1753271987472, "results": "244", "hashOfConfig": "127"}, {"size": 7528, "mtime": 1753268887508, "results": "245", "hashOfConfig": "127"}, {"size": 11498, "mtime": 1753271050611, "results": "246", "hashOfConfig": "127"}, {"size": 4707, "mtime": 1753268739564, "results": "247", "hashOfConfig": "127"}, {"size": 3041, "mtime": 1753320485065, "results": "248", "hashOfConfig": "127"}, {"size": 10058, "mtime": 1753320440476, "results": "249", "hashOfConfig": "127"}, {"size": 5939, "mtime": 1753320468838, "results": "250", "hashOfConfig": "127"}, {"size": 12031, "mtime": 1753271208137, "results": "251", "hashOfConfig": "127"}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xa5pjk", {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 43, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\authContext.tsx", ["627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\playerContext.tsx", ["638", "639", "640"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\hostContext.tsx", ["641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\timeListenerContext.tsx", ["653", "654", "655", "656", "657", "658", "659", "660"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\soundContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\routes\\ProtectedRoute.tsx", ["661"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.tsx", ["662", "663", "664", "665"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\FallBack.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Spectator\\SpectatorJoin.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.tsx", ["666"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\JoinRoom\\JoinRoom.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Login\\Login.tsx", ["667"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.tsx", ["668", "669"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.tsx", ["670", "671", "672", "673", "674", "675", "676", "677", "678"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.tsx", ["679", "680"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.tsx", ["681"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.tsx", ["682", "683", "684", "685", "686"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Home\\Home.tsx", ["687"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.tsx", ["688", "689", "690"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Room\\CreateRoom.tsx", ["691", "692", "693", "694"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound3.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRoundTurn.tsx", ["695", "696"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound4.tsx", ["697", "698"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound2.tsx", ["699"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\Dashboard.tsx", ["700"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound1.tsx", ["701"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\services.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\service.ts", ["702", "703"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\service.ts", ["704", "705"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\firebaseServices.ts", ["706", "707", "708", "709"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\services.ts", ["710", "711"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\auth.service.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.tsx", ["712", "713", "714", "715", "716"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useAuth.ts", ["717", "718", "719"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\uploadAssestServices.ts", ["720"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\room.service.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.tsx", ["721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.tsx", ["733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.tsx", ["762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\UploadTest.tsx", ["791", "792"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\SetUpMatch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\ViewTest.tsx", ["793", "794", "795", "796", "797", "798"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRoundTurn.tsx", ["799", "800", "801", "802", "803", "804", "805", "806", "807", "808"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRound4.tsx", ["809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\History.tsx", ["830", "831", "832", "833", "834", "835", "836", "837", "838", "839"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Host\\Host.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRoundTurn.tsx", ["840", "841", "842", "843", "844", "845", "846", "847", "848", "849"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRound4.tsx", ["850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\http.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\firebase-config.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.tsx", ["876"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\services.ts", ["877"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\testManagement.service.ts", ["878"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\utils.ts", ["879", "880", "881", "882", "883", "884", "885", "886", "887"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useListener.ts", ["888", "889", "890", "891", "892", "893", "894", "895", "896", "897"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\PlayerAnswerInput.tsx", ["898"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\GameGrid.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.tsx", ["899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.tsx", ["942", "943", "944", "945", "946"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostManagement.tsx", ["947", "948", "949", "950", "951", "952", "953", "954"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostAnswer.tsx", ["955", "956", "957", "958", "959", "960", "961"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\utils\\processFile.utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostQuestionPreview.tsx", ["962", "963", "964", "965"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\RulesModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostGuideModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerColorSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\SimpleColorPicker.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\tokenRefresh.service.ts", ["966"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useTokenRefresh.ts", ["967"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\providers\\ReduxProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\authSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\uiSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\roomSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\gameSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.migrated.tsx", ["968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.migrated.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round4.migrated.tsx", ["979", "980", "981", "982", "983", "984", "985", "986"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.migrated.tsx", ["987", "988"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.migrated.tsx", ["989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.migrated.tsx", ["1027", "1028"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.migrated.tsx", ["1029", "1030"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.migrated.tsx", ["1031", "1032", "1033"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.migrated.tsx", ["1034", "1035", "1036", "1037", "1038"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\firebase\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\firebase\\useFirebaseListener.ts", ["1039"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useLocalStorage.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useDebounce.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\common\\useAsync.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\useAuthApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\hooks\\api\\useGameApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\Input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\Button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Button\\Button.variants.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\components\\ui\\Input\\Input.variants.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\auth\\tokenService.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\game\\gameApi.ts", ["1040", "1041"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\firebase\\realtime.ts", ["1042", "1043", "1044"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\auth\\authApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\firebase\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\services\\api\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\api.constants.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\constants\\game.constants.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.migrated.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.migrated.tsx", ["1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.migrated.tsx", ["1057"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.migrated.tsx", ["1058", "1059", "1060", "1061", "1062", "1063"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\shared\\utils\\migration.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.migrated.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.migrated.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.migrated.tsx", ["1064", "1065", "1066", "1067"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.migrated.tsx", ["1068", "1069", "1070", "1071"], [], {"ruleId": "1072", "severity": 1, "message": "1073", "line": 2, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1076", "line": 3, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 3, "endColumn": 15}, {"ruleId": "1072", "severity": 1, "message": "1077", "line": 5, "column": 3, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 10}, {"ruleId": "1072", "severity": 1, "message": "1078", "line": 6, "column": 3, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1079", "line": 8, "column": 3, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 10}, {"ruleId": "1072", "severity": 1, "message": "1080", "line": 9, "column": 3, "nodeType": "1074", "messageId": "1075", "endLine": 9, "endColumn": 21}, {"ruleId": "1072", "severity": 1, "message": "1081", "line": 17, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 23}, {"ruleId": "1072", "severity": 1, "message": "1082", "line": 17, "column": 25, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 41}, {"ruleId": "1072", "severity": 1, "message": "1083", "line": 18, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 18, "endColumn": 14}, {"ruleId": "1072", "severity": 1, "message": "1084", "line": 18, "column": 16, "nodeType": "1074", "messageId": "1075", "endLine": 18, "endColumn": 23}, {"ruleId": "1072", "severity": 1, "message": "1085", "line": 21, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 21, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1073", "line": 2, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1086", "line": 17, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 22}, {"ruleId": "1072", "severity": 1, "message": "1087", "line": 17, "column": 24, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 39}, {"ruleId": "1072", "severity": 1, "message": "1088", "line": 7, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 7, "endColumn": 27}, {"ruleId": "1072", "severity": 1, "message": "1089", "line": 8, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1090", "line": 8, "column": 27, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 51}, {"ruleId": "1072", "severity": 1, "message": "1091", "line": 10, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 10, "endColumn": 15}, {"ruleId": "1072", "severity": 1, "message": "1092", "line": 15, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 15, "endColumn": 35}, {"ruleId": "1072", "severity": 1, "message": "1093", "line": 20, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 20, "endColumn": 15}, {"ruleId": "1072", "severity": 1, "message": "1094", "line": 21, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 21, "endColumn": 15}, {"ruleId": "1072", "severity": 1, "message": "1095", "line": 27, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 27, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1096", "line": 45, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 45, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1097", "line": 45, "column": 20, "nodeType": "1074", "messageId": "1075", "endLine": 45, "endColumn": 31}, {"ruleId": "1072", "severity": 1, "message": "1098", "line": 56, "column": 24, "nodeType": "1074", "messageId": "1075", "endLine": 56, "endColumn": 39}, {"ruleId": "1099", "severity": 1, "message": "1100", "line": 72, "column": 6, "nodeType": "1101", "endLine": 72, "endColumn": 8, "suggestions": "1102"}, {"ruleId": "1072", "severity": 1, "message": "1073", "line": 1, "column": 44, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 53}, {"ruleId": "1072", "severity": 1, "message": "1103", "line": 2, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1104", "line": 2, "column": 22, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 39}, {"ruleId": "1072", "severity": 1, "message": "1091", "line": 4, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 15}, {"ruleId": "1072", "severity": 1, "message": "1105", "line": 6, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1094", "line": 30, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 30, "endColumn": 15}, {"ruleId": "1072", "severity": 1, "message": "1106", "line": 33, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 33, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1107", "line": 71, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 71, "endColumn": 23}, {"ruleId": "1099", "severity": 1, "message": "1108", "line": 130, "column": 8, "nodeType": "1101", "endLine": 130, "endColumn": 60, "suggestions": "1109"}, {"ruleId": "1072", "severity": 1, "message": "1110", "line": 1, "column": 38, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 47}, {"ruleId": "1072", "severity": 1, "message": "1111", "line": 1, "column": 49, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 55}, {"ruleId": "1072", "severity": 1, "message": "1112", "line": 1, "column": 57, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 68}, {"ruleId": "1099", "severity": 1, "message": "1113", "line": 54, "column": 6, "nodeType": "1101", "endLine": 54, "endColumn": 8, "suggestions": "1114"}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 27, "column": 8, "nodeType": "1101", "endLine": 27, "endColumn": 10, "suggestions": "1116"}, {"ruleId": "1117", "severity": 1, "message": "1118", "line": 100, "column": 17, "nodeType": "1119", "endLine": 100, "endColumn": 101}, {"ruleId": "1072", "severity": 1, "message": "1120", "line": 20, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 20, "endColumn": 21}, {"ruleId": "1099", "severity": 1, "message": "1121", "line": 66, "column": 8, "nodeType": "1101", "endLine": 66, "endColumn": 16, "suggestions": "1122"}, {"ruleId": "1072", "severity": 1, "message": "1123", "line": 4, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1124", "line": 6, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1125", "line": 17, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 22}, {"ruleId": "1072", "severity": 1, "message": "1126", "line": 17, "column": 24, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 39}, {"ruleId": "1072", "severity": 1, "message": "1127", "line": 18, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 18, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1128", "line": 18, "column": 21, "nodeType": "1074", "messageId": "1075", "endLine": 18, "endColumn": 33}, {"ruleId": "1072", "severity": 1, "message": "1129", "line": 21, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 21, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1120", "line": 28, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 28, "endColumn": 19}, {"ruleId": "1099", "severity": 1, "message": "1121", "line": 73, "column": 6, "nodeType": "1101", "endLine": 73, "endColumn": 14, "suggestions": "1130"}, {"ruleId": "1072", "severity": 1, "message": "1120", "line": 20, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 20, "endColumn": 21}, {"ruleId": "1099", "severity": 1, "message": "1121", "line": 66, "column": 8, "nodeType": "1101", "endLine": 66, "endColumn": 16, "suggestions": "1131"}, {"ruleId": "1072", "severity": 1, "message": "1132", "line": 3, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 3, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1133", "line": 1, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 14}, {"ruleId": "1072", "severity": 1, "message": "1134", "line": 8, "column": 7, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1135", "line": 36, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 36, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1120", "line": 38, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 38, "endColumn": 21}, {"ruleId": "1099", "severity": 1, "message": "1121", "line": 83, "column": 8, "nodeType": "1101", "endLine": 83, "endColumn": 16, "suggestions": "1136"}, {"ruleId": "1099", "severity": 1, "message": "1137", "line": 20, "column": 6, "nodeType": "1101", "endLine": 20, "endColumn": 8, "suggestions": "1138"}, {"ruleId": "1072", "severity": 1, "message": "1129", "line": 14, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 14, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1120", "line": 22, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 22, "endColumn": 21}, {"ruleId": "1099", "severity": 1, "message": "1121", "line": 67, "column": 8, "nodeType": "1101", "endLine": 67, "endColumn": 16, "suggestions": "1139"}, {"ruleId": "1072", "severity": 1, "message": "1073", "line": 1, "column": 27, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 36}, {"ruleId": "1072", "severity": 1, "message": "1140", "line": 3, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 3, "endColumn": 14}, {"ruleId": "1072", "severity": 1, "message": "1141", "line": 4, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 12}, {"ruleId": "1072", "severity": 1, "message": "1142", "line": 7, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 7, "endColumn": 14}, {"ruleId": "1072", "severity": 1, "message": "1143", "line": 1, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 14}, {"ruleId": "1072", "severity": 1, "message": "1144", "line": 4, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1133", "line": 1, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 14}, {"ruleId": "1072", "severity": 1, "message": "1145", "line": 7, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 7, "endColumn": 12}, {"ruleId": "1099", "severity": 1, "message": "1113", "line": 32, "column": 7, "nodeType": "1101", "endLine": 32, "endColumn": 9, "suggestions": "1146"}, {"ruleId": "1072", "severity": 1, "message": "1147", "line": 15, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 15, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1143", "line": 1, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 14}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 60, "column": 25, "nodeType": "1150", "messageId": "1151", "endLine": 60, "endColumn": 27}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 65, "column": 61, "nodeType": "1150", "messageId": "1151", "endLine": 65, "endColumn": 63}, {"ruleId": "1072", "severity": 1, "message": "1152", "line": 1, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1153", "line": 2, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1152", "line": 3, "column": 16, "nodeType": "1074", "messageId": "1075", "endLine": 3, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1073", "line": 5, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1154", "line": 19, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 19, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1155", "line": 27, "column": 5, "nodeType": "1074", "messageId": "1075", "endLine": 27, "endColumn": 18}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 89, "column": 25, "nodeType": "1150", "messageId": "1151", "endLine": 89, "endColumn": 27}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 94, "column": 61, "nodeType": "1150", "messageId": "1151", "endLine": 94, "endColumn": 63}, {"ruleId": "1072", "severity": 1, "message": "1156", "line": 1, "column": 17, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1073", "line": 1, "column": 27, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 36}, {"ruleId": "1072", "severity": 1, "message": "1111", "line": 1, "column": 49, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 55}, {"ruleId": "1072", "severity": 1, "message": "1112", "line": 1, "column": 57, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 68}, {"ruleId": "1072", "severity": 1, "message": "1157", "line": 4, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 23}, {"ruleId": "1072", "severity": 1, "message": "1158", "line": 14, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 14, "endColumn": 26}, {"ruleId": "1072", "severity": 1, "message": "1129", "line": 22, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 22, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1159", "line": 103, "column": 15, "nodeType": "1074", "messageId": "1075", "endLine": 103, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1160", "line": 33, "column": 16, "nodeType": "1074", "messageId": "1075", "endLine": 33, "endColumn": 41}, {"ruleId": "1072", "severity": 1, "message": "1145", "line": 2, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 12}, {"ruleId": "1072", "severity": 1, "message": "1161", "line": 3, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 3, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1162", "line": 13, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 13, "endColumn": 13}, {"ruleId": "1072", "severity": 1, "message": "1163", "line": 32, "column": 24, "nodeType": "1074", "messageId": "1075", "endLine": 32, "endColumn": 37}, {"ruleId": "1072", "severity": 1, "message": "1097", "line": 35, "column": 53, "nodeType": "1074", "messageId": "1075", "endLine": 35, "endColumn": 64}, {"ruleId": "1072", "severity": 1, "message": "1164", "line": 36, "column": 55, "nodeType": "1074", "messageId": "1075", "endLine": 36, "endColumn": 67}, {"ruleId": "1099", "severity": 1, "message": "1165", "line": 40, "column": 8, "nodeType": "1101", "endLine": 40, "endColumn": 33, "suggestions": "1166"}, {"ruleId": "1099", "severity": 1, "message": "1167", "line": 71, "column": 8, "nodeType": "1101", "endLine": 71, "endColumn": 10, "suggestions": "1168"}, {"ruleId": "1099", "severity": 1, "message": "1169", "line": 96, "column": 8, "nodeType": "1101", "endLine": 96, "endColumn": 18, "suggestions": "1170"}, {"ruleId": "1099", "severity": 1, "message": "1171", "line": 119, "column": 8, "nodeType": "1101", "endLine": 119, "endColumn": 10, "suggestions": "1172"}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 136, "column": 8, "nodeType": "1101", "endLine": 136, "endColumn": 10, "suggestions": "1173"}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 153, "column": 8, "nodeType": "1101", "endLine": 153, "endColumn": 10, "suggestions": "1174"}, {"ruleId": "1072", "severity": 1, "message": "1145", "line": 1, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 12}, {"ruleId": "1072", "severity": 1, "message": "1175", "line": 3, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 3, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1176", "line": 18, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 18, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1177", "line": 54, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 54, "endColumn": 27}, {"ruleId": "1072", "severity": 1, "message": "1178", "line": 60, "column": 7, "nodeType": "1074", "messageId": "1075", "endLine": 60, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1093", "line": 72, "column": 5, "nodeType": "1074", "messageId": "1075", "endLine": 72, "endColumn": 11}, {"ruleId": "1072", "severity": 1, "message": "1097", "line": 74, "column": 33, "nodeType": "1074", "messageId": "1075", "endLine": 74, "endColumn": 44}, {"ruleId": "1072", "severity": 1, "message": "1164", "line": 77, "column": 27, "nodeType": "1074", "messageId": "1075", "endLine": 77, "endColumn": 39}, {"ruleId": "1072", "severity": 1, "message": "1179", "line": 101, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 101, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1180", "line": 101, "column": 27, "nodeType": "1074", "messageId": "1075", "endLine": 101, "endColumn": 45}, {"ruleId": "1072", "severity": 1, "message": "1181", "line": 102, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 102, "endColumn": 26}, {"ruleId": "1072", "severity": 1, "message": "1182", "line": 103, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 103, "endColumn": 31}, {"ruleId": "1099", "severity": 1, "message": "1169", "line": 122, "column": 6, "nodeType": "1101", "endLine": 122, "endColumn": 16, "suggestions": "1183"}, {"ruleId": "1099", "severity": 1, "message": "1167", "line": 142, "column": 6, "nodeType": "1101", "endLine": 142, "endColumn": 8, "suggestions": "1184"}, {"ruleId": "1072", "severity": 1, "message": "1185", "line": 147, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 147, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1186", "line": 148, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 148, "endColumn": 22}, {"ruleId": "1187", "severity": 1, "message": "1188", "line": 154, "column": 34, "nodeType": "1189", "messageId": "1190", "endLine": 154, "endColumn": 36}, {"ruleId": "1072", "severity": 1, "message": "1191", "line": 206, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 206, "endColumn": 19}, {"ruleId": "1099", "severity": 1, "message": "1192", "line": 232, "column": 6, "nodeType": "1101", "endLine": 232, "endColumn": 14, "suggestions": "1193"}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 251, "column": 6, "nodeType": "1101", "endLine": 251, "endColumn": 8, "suggestions": "1194"}, {"ruleId": "1187", "severity": 1, "message": "1188", "line": 293, "column": 38, "nodeType": "1189", "messageId": "1190", "endLine": 293, "endColumn": 40}, {"ruleId": "1099", "severity": 1, "message": "1195", "line": 326, "column": 6, "nodeType": "1101", "endLine": 326, "endColumn": 48, "suggestions": "1196"}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 376, "column": 19, "nodeType": "1150", "messageId": "1151", "endLine": 376, "endColumn": 21}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 436, "column": 19, "nodeType": "1150", "messageId": "1151", "endLine": 436, "endColumn": 21}, {"ruleId": "1099", "severity": 1, "message": "1197", "line": 633, "column": 6, "nodeType": "1101", "endLine": 633, "endColumn": 20, "suggestions": "1198"}, {"ruleId": "1099", "severity": 1, "message": "1199", "line": 647, "column": 6, "nodeType": "1101", "endLine": 647, "endColumn": 8, "suggestions": "1200"}, {"ruleId": "1099", "severity": 1, "message": "1201", "line": 683, "column": 6, "nodeType": "1101", "endLine": 683, "endColumn": 20, "suggestions": "1202"}, {"ruleId": "1099", "severity": 1, "message": "1203", "line": 727, "column": 6, "nodeType": "1101", "endLine": 727, "endColumn": 20, "suggestions": "1204"}, {"ruleId": "1099", "severity": 1, "message": "1203", "line": 780, "column": 6, "nodeType": "1101", "endLine": 780, "endColumn": 20, "suggestions": "1205"}, {"ruleId": "1072", "severity": 1, "message": "1145", "line": 2, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 12}, {"ruleId": "1072", "severity": 1, "message": "1161", "line": 3, "column": 20, "nodeType": "1074", "messageId": "1075", "endLine": 3, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1206", "line": 6, "column": 97, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 127}, {"ruleId": "1072", "severity": 1, "message": "1090", "line": 9, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 9, "endColumn": 34}, {"ruleId": "1072", "severity": 1, "message": "1207", "line": 12, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 12, "endColumn": 27}, {"ruleId": "1072", "severity": 1, "message": "1208", "line": 31, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 31, "endColumn": 30}, {"ruleId": "1072", "severity": 1, "message": "1209", "line": 36, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 36, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1164", "line": 40, "column": 83, "nodeType": "1074", "messageId": "1075", "endLine": 40, "endColumn": 95}, {"ruleId": "1072", "severity": 1, "message": "1210", "line": 41, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 41, "endColumn": 33}, {"ruleId": "1072", "severity": 1, "message": "1211", "line": 41, "column": 129, "nodeType": "1074", "messageId": "1075", "endLine": 41, "endColumn": 148}, {"ruleId": "1072", "severity": 1, "message": "1212", "line": 42, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 42, "endColumn": 38}, {"ruleId": "1072", "severity": 1, "message": "1213", "line": 43, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 43, "endColumn": 30}, {"ruleId": "1072", "severity": 1, "message": "1214", "line": 44, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 44, "endColumn": 28}, {"ruleId": "1072", "severity": 1, "message": "1215", "line": 44, "column": 30, "nodeType": "1074", "messageId": "1075", "endLine": 44, "endColumn": 49}, {"ruleId": "1072", "severity": 1, "message": "1216", "line": 48, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 48, "endColumn": 16}, {"ruleId": "1072", "severity": 1, "message": "1217", "line": 49, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 49, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1097", "line": 50, "column": 35, "nodeType": "1074", "messageId": "1075", "endLine": 50, "endColumn": 46}, {"ruleId": "1072", "severity": 1, "message": "1218", "line": 52, "column": 14, "nodeType": "1074", "messageId": "1075", "endLine": 52, "endColumn": 28}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 118, "column": 8, "nodeType": "1101", "endLine": 118, "endColumn": 10, "suggestions": "1219"}, {"ruleId": "1072", "severity": 1, "message": "1220", "line": 126, "column": 19, "nodeType": "1074", "messageId": "1075", "endLine": 126, "endColumn": 26}, {"ruleId": "1099", "severity": 1, "message": "1108", "line": 137, "column": 8, "nodeType": "1101", "endLine": 137, "endColumn": 10, "suggestions": "1221"}, {"ruleId": "1099", "severity": 1, "message": "1222", "line": 174, "column": 8, "nodeType": "1101", "endLine": 174, "endColumn": 24, "suggestions": "1223"}, {"ruleId": "1099", "severity": 1, "message": "1167", "line": 207, "column": 8, "nodeType": "1101", "endLine": 207, "endColumn": 10, "suggestions": "1224"}, {"ruleId": "1099", "severity": 1, "message": "1225", "line": 236, "column": 8, "nodeType": "1101", "endLine": 236, "endColumn": 18, "suggestions": "1226"}, {"ruleId": "1099", "severity": 1, "message": "1227", "line": 246, "column": 8, "nodeType": "1101", "endLine": 246, "endColumn": 10, "suggestions": "1228"}, {"ruleId": "1099", "severity": 1, "message": "1108", "line": 259, "column": 8, "nodeType": "1101", "endLine": 259, "endColumn": 10, "suggestions": "1229"}, {"ruleId": "1099", "severity": 1, "message": "1230", "line": 279, "column": 8, "nodeType": "1101", "endLine": 279, "endColumn": 10, "suggestions": "1231"}, {"ruleId": "1072", "severity": 1, "message": "1220", "line": 293, "column": 19, "nodeType": "1074", "messageId": "1075", "endLine": 293, "endColumn": 26}, {"ruleId": "1099", "severity": 1, "message": "1232", "line": 316, "column": 8, "nodeType": "1101", "endLine": 316, "endColumn": 10, "suggestions": "1233"}, {"ruleId": "1072", "severity": 1, "message": "1234", "line": 2, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 28}, {"ruleId": "1072", "severity": 1, "message": "1235", "line": 18, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 18, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1236", "line": 2, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1237", "line": 2, "column": 19, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 34}, {"ruleId": "1072", "severity": 1, "message": "1238", "line": 2, "column": 52, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 66}, {"ruleId": "1072", "severity": 1, "message": "1239", "line": 5, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1240", "line": 149, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 149, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1241", "line": 160, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 160, "endColumn": 26}, {"ruleId": "1072", "severity": 1, "message": "1162", "line": 12, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 12, "endColumn": 13}, {"ruleId": "1072", "severity": 1, "message": "1163", "line": 31, "column": 24, "nodeType": "1074", "messageId": "1075", "endLine": 31, "endColumn": 37}, {"ruleId": "1072", "severity": 1, "message": "1097", "line": 33, "column": 53, "nodeType": "1074", "messageId": "1075", "endLine": 33, "endColumn": 64}, {"ruleId": "1072", "severity": 1, "message": "1164", "line": 34, "column": 55, "nodeType": "1074", "messageId": "1075", "endLine": 34, "endColumn": 67}, {"ruleId": "1099", "severity": 1, "message": "1165", "line": 38, "column": 8, "nodeType": "1101", "endLine": 38, "endColumn": 33, "suggestions": "1242"}, {"ruleId": "1099", "severity": 1, "message": "1167", "line": 68, "column": 8, "nodeType": "1101", "endLine": 68, "endColumn": 10, "suggestions": "1243"}, {"ruleId": "1099", "severity": 1, "message": "1169", "line": 93, "column": 8, "nodeType": "1101", "endLine": 93, "endColumn": 18, "suggestions": "1244"}, {"ruleId": "1099", "severity": 1, "message": "1171", "line": 116, "column": 8, "nodeType": "1101", "endLine": 116, "endColumn": 10, "suggestions": "1245"}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 133, "column": 8, "nodeType": "1101", "endLine": 133, "endColumn": 10, "suggestions": "1246"}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 150, "column": 8, "nodeType": "1101", "endLine": 150, "endColumn": 10, "suggestions": "1247"}, {"ruleId": "1072", "severity": 1, "message": "1103", "line": 4, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1248", "line": 4, "column": 41, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 54}, {"ruleId": "1072", "severity": 1, "message": "1249", "line": 4, "column": 56, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 73}, {"ruleId": "1072", "severity": 1, "message": "1250", "line": 4, "column": 75, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 95}, {"ruleId": "1072", "severity": 1, "message": "1251", "line": 4, "column": 97, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 114}, {"ruleId": "1072", "severity": 1, "message": "1252", "line": 4, "column": 116, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 131}, {"ruleId": "1072", "severity": 1, "message": "1124", "line": 4, "column": 133, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 148}, {"ruleId": "1072", "severity": 1, "message": "1253", "line": 4, "column": 150, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 162}, {"ruleId": "1072", "severity": 1, "message": "1254", "line": 20, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 20, "endColumn": 43}, {"ruleId": "1072", "severity": 1, "message": "1097", "line": 28, "column": 35, "nodeType": "1074", "messageId": "1075", "endLine": 28, "endColumn": 46}, {"ruleId": "1072", "severity": 1, "message": "1255", "line": 29, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 29, "endColumn": 28}, {"ruleId": "1072", "severity": 1, "message": "1256", "line": 29, "column": 30, "nodeType": "1074", "messageId": "1075", "endLine": 29, "endColumn": 49}, {"ruleId": "1072", "severity": 1, "message": "1257", "line": 35, "column": 18, "nodeType": "1074", "messageId": "1075", "endLine": 35, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1258", "line": 40, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 40, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1259", "line": 44, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 44, "endColumn": 34}, {"ruleId": "1072", "severity": 1, "message": "1260", "line": 44, "column": 36, "nodeType": "1074", "messageId": "1075", "endLine": 44, "endColumn": 59}, {"ruleId": "1072", "severity": 1, "message": "1261", "line": 44, "column": 61, "nodeType": "1074", "messageId": "1075", "endLine": 44, "endColumn": 82}, {"ruleId": "1072", "severity": 1, "message": "1262", "line": 44, "column": 84, "nodeType": "1074", "messageId": "1075", "endLine": 44, "endColumn": 92}, {"ruleId": "1072", "severity": 1, "message": "1164", "line": 44, "column": 94, "nodeType": "1074", "messageId": "1075", "endLine": 44, "endColumn": 106}, {"ruleId": "1099", "severity": 1, "message": "1225", "line": 83, "column": 8, "nodeType": "1101", "endLine": 83, "endColumn": 18, "suggestions": "1263"}, {"ruleId": "1099", "severity": 1, "message": "1167", "line": 102, "column": 8, "nodeType": "1101", "endLine": 102, "endColumn": 10, "suggestions": "1264"}, {"ruleId": "1072", "severity": 1, "message": "1111", "line": 1, "column": 38, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 44}, {"ruleId": "1072", "severity": 1, "message": "1236", "line": 2, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1237", "line": 2, "column": 19, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 34}, {"ruleId": "1072", "severity": 1, "message": "1265", "line": 2, "column": 36, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 50}, {"ruleId": "1072", "severity": 1, "message": "1238", "line": 2, "column": 52, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 66}, {"ruleId": "1072", "severity": 1, "message": "1266", "line": 3, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 3, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1152", "line": 4, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1239", "line": 5, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1267", "line": 6, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1268", "line": 27, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 27, "endColumn": 30}, {"ruleId": "1072", "severity": 1, "message": "1162", "line": 12, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 12, "endColumn": 13}, {"ruleId": "1072", "severity": 1, "message": "1163", "line": 31, "column": 24, "nodeType": "1074", "messageId": "1075", "endLine": 31, "endColumn": 37}, {"ruleId": "1072", "severity": 1, "message": "1097", "line": 33, "column": 53, "nodeType": "1074", "messageId": "1075", "endLine": 33, "endColumn": 64}, {"ruleId": "1072", "severity": 1, "message": "1164", "line": 34, "column": 55, "nodeType": "1074", "messageId": "1075", "endLine": 34, "endColumn": 67}, {"ruleId": "1099", "severity": 1, "message": "1165", "line": 38, "column": 8, "nodeType": "1101", "endLine": 38, "endColumn": 33, "suggestions": "1269"}, {"ruleId": "1099", "severity": 1, "message": "1167", "line": 68, "column": 8, "nodeType": "1101", "endLine": 68, "endColumn": 10, "suggestions": "1270"}, {"ruleId": "1099", "severity": 1, "message": "1169", "line": 93, "column": 8, "nodeType": "1101", "endLine": 93, "endColumn": 18, "suggestions": "1271"}, {"ruleId": "1099", "severity": 1, "message": "1171", "line": 116, "column": 8, "nodeType": "1101", "endLine": 116, "endColumn": 10, "suggestions": "1272"}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 133, "column": 8, "nodeType": "1101", "endLine": 133, "endColumn": 10, "suggestions": "1273"}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 150, "column": 8, "nodeType": "1101", "endLine": 150, "endColumn": 10, "suggestions": "1274"}, {"ruleId": "1072", "severity": 1, "message": "1145", "line": 2, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 12}, {"ruleId": "1072", "severity": 1, "message": "1161", "line": 3, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 3, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1103", "line": 8, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1104", "line": 8, "column": 22, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 39}, {"ruleId": "1072", "severity": 1, "message": "1248", "line": 8, "column": 41, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 54}, {"ruleId": "1072", "severity": 1, "message": "1249", "line": 8, "column": 56, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 73}, {"ruleId": "1072", "severity": 1, "message": "1250", "line": 8, "column": 75, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 95}, {"ruleId": "1072", "severity": 1, "message": "1251", "line": 8, "column": 97, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 114}, {"ruleId": "1072", "severity": 1, "message": "1252", "line": 8, "column": 116, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 131}, {"ruleId": "1072", "severity": 1, "message": "1124", "line": 8, "column": 133, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 148}, {"ruleId": "1072", "severity": 1, "message": "1253", "line": 8, "column": 150, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 162}, {"ruleId": "1072", "severity": 1, "message": "1275", "line": 20, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 20, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1134", "line": 31, "column": 7, "nodeType": "1074", "messageId": "1075", "endLine": 31, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1276", "line": 40, "column": 7, "nodeType": "1074", "messageId": "1075", "endLine": 40, "endColumn": 23}, {"ruleId": "1072", "severity": 1, "message": "1097", "line": 230, "column": 35, "nodeType": "1074", "messageId": "1075", "endLine": 230, "endColumn": 46}, {"ruleId": "1072", "severity": 1, "message": "1255", "line": 231, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 231, "endColumn": 28}, {"ruleId": "1072", "severity": 1, "message": "1259", "line": 246, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 246, "endColumn": 34}, {"ruleId": "1072", "severity": 1, "message": "1260", "line": 246, "column": 36, "nodeType": "1074", "messageId": "1075", "endLine": 246, "endColumn": 59}, {"ruleId": "1072", "severity": 1, "message": "1261", "line": 246, "column": 61, "nodeType": "1074", "messageId": "1075", "endLine": 246, "endColumn": 82}, {"ruleId": "1072", "severity": 1, "message": "1164", "line": 246, "column": 94, "nodeType": "1074", "messageId": "1075", "endLine": 246, "endColumn": 106}, {"ruleId": "1099", "severity": 1, "message": "1277", "line": 274, "column": 8, "nodeType": "1101", "endLine": 274, "endColumn": 10, "suggestions": "1278"}, {"ruleId": "1099", "severity": 1, "message": "1225", "line": 294, "column": 8, "nodeType": "1101", "endLine": 294, "endColumn": 18, "suggestions": "1279"}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 347, "column": 39, "nodeType": "1150", "messageId": "1151", "endLine": 347, "endColumn": 41}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 351, "column": 39, "nodeType": "1150", "messageId": "1151", "endLine": 351, "endColumn": 41}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 355, "column": 39, "nodeType": "1150", "messageId": "1151", "endLine": 355, "endColumn": 41}, {"ruleId": "1072", "severity": 1, "message": "1280", "line": 409, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 409, "endColumn": 30}, {"ruleId": "1072", "severity": 1, "message": "1281", "line": 13, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 13, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1153", "line": 1, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1282", "line": 25, "column": 15, "nodeType": "1074", "messageId": "1075", "endLine": 25, "endColumn": 27}, {"ruleId": "1072", "severity": 1, "message": "1152", "line": 10, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 10, "endColumn": 21}, {"ruleId": "1072", "severity": 1, "message": "1283", "line": 69, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 69, "endColumn": 21}, {"ruleId": "1072", "severity": 1, "message": "1284", "line": 131, "column": 45, "nodeType": "1074", "messageId": "1075", "endLine": 131, "endColumn": 53}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 150, "column": 17, "nodeType": "1150", "messageId": "1151", "endLine": 150, "endColumn": 19}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 155, "column": 21, "nodeType": "1150", "messageId": "1151", "endLine": 155, "endColumn": 23}, {"ruleId": "1285", "severity": 1, "message": "1286", "line": 259, "column": 11, "nodeType": "1074", "messageId": "1287", "endLine": 259, "endColumn": 17}, {"ruleId": "1285", "severity": 1, "message": "1288", "line": 260, "column": 9, "nodeType": "1074", "messageId": "1287", "endLine": 260, "endColumn": 15}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 272, "column": 34, "nodeType": "1150", "messageId": "1151", "endLine": 272, "endColumn": 36}, {"ruleId": "1148", "severity": 1, "message": "1289", "line": 273, "column": 23, "nodeType": "1150", "messageId": "1151", "endLine": 273, "endColumn": 25}, {"ruleId": "1099", "severity": 1, "message": "1290", "line": 59, "column": 8, "nodeType": "1101", "endLine": 59, "endColumn": 10, "suggestions": "1291"}, {"ruleId": "1072", "severity": 1, "message": "1191", "line": 62, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 62, "endColumn": 23}, {"ruleId": "1099", "severity": 1, "message": "1292", "line": 88, "column": 8, "nodeType": "1101", "endLine": 88, "endColumn": 16, "suggestions": "1293"}, {"ruleId": "1072", "severity": 1, "message": "1191", "line": 92, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 92, "endColumn": 23}, {"ruleId": "1099", "severity": 1, "message": "1294", "line": 118, "column": 8, "nodeType": "1101", "endLine": 118, "endColumn": 16, "suggestions": "1295"}, {"ruleId": "1099", "severity": 1, "message": "1115", "line": 135, "column": 8, "nodeType": "1101", "endLine": 135, "endColumn": 10, "suggestions": "1296"}, {"ruleId": "1099", "severity": 1, "message": "1297", "line": 157, "column": 8, "nodeType": "1101", "endLine": 157, "endColumn": 10, "suggestions": "1298"}, {"ruleId": "1099", "severity": 1, "message": "1299", "line": 170, "column": 8, "nodeType": "1101", "endLine": 170, "endColumn": 10, "suggestions": "1300"}, {"ruleId": "1099", "severity": 1, "message": "1301", "line": 210, "column": 8, "nodeType": "1101", "endLine": 210, "endColumn": 10, "suggestions": "1302"}, {"ruleId": "1099", "severity": 1, "message": "1303", "line": 238, "column": 8, "nodeType": "1101", "endLine": 238, "endColumn": 10, "suggestions": "1304"}, {"ruleId": "1099", "severity": 1, "message": "1305", "line": 40, "column": 8, "nodeType": "1101", "endLine": 40, "endColumn": 18, "suggestions": "1306"}, {"ruleId": "1072", "severity": 1, "message": "1112", "line": 1, "column": 57, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 68}, {"ruleId": "1072", "severity": 1, "message": "1307", "line": 4, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 16}, {"ruleId": "1072", "severity": 1, "message": "1308", "line": 4, "column": 18, "nodeType": "1074", "messageId": "1075", "endLine": 4, "endColumn": 22}, {"ruleId": "1072", "severity": 1, "message": "1309", "line": 5, "column": 22, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 37}, {"ruleId": "1072", "severity": 1, "message": "1310", "line": 5, "column": 39, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 52}, {"ruleId": "1072", "severity": 1, "message": "1311", "line": 5, "column": 93, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 107}, {"ruleId": "1072", "severity": 1, "message": "1252", "line": 5, "column": 109, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 124}, {"ruleId": "1072", "severity": 1, "message": "1104", "line": 5, "column": 126, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 143}, {"ruleId": "1072", "severity": 1, "message": "1312", "line": 5, "column": 145, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 170}, {"ruleId": "1072", "severity": 1, "message": "1313", "line": 5, "column": 191, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 209}, {"ruleId": "1072", "severity": 1, "message": "1314", "line": 7, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 7, "endColumn": 22}, {"ruleId": "1072", "severity": 1, "message": "1315", "line": 8, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1316", "line": 10, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 10, "endColumn": 22}, {"ruleId": "1072", "severity": 1, "message": "1317", "line": 11, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 11, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1318", "line": 13, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 13, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1090", "line": 14, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 14, "endColumn": 34}, {"ruleId": "1072", "severity": 1, "message": "1319", "line": 17, "column": 5, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 12}, {"ruleId": "1072", "severity": 1, "message": "1320", "line": 29, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 29, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1321", "line": 57, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 57, "endColumn": 26}, {"ruleId": "1072", "severity": 1, "message": "1322", "line": 63, "column": 20, "nodeType": "1074", "messageId": "1075", "endLine": 63, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1323", "line": 66, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1324", "line": 66, "column": 22, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 32}, {"ruleId": "1072", "severity": 1, "message": "1325", "line": 66, "column": 34, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 43}, {"ruleId": "1072", "severity": 1, "message": "1326", "line": 66, "column": 45, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 57}, {"ruleId": "1072", "severity": 1, "message": "1327", "line": 66, "column": 59, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 68}, {"ruleId": "1072", "severity": 1, "message": "1328", "line": 66, "column": 100, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 108}, {"ruleId": "1072", "severity": 1, "message": "1329", "line": 66, "column": 110, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 128}, {"ruleId": "1072", "severity": 1, "message": "1330", "line": 66, "column": 130, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 143}, {"ruleId": "1072", "severity": 1, "message": "1331", "line": 66, "column": 145, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 161}, {"ruleId": "1072", "severity": 1, "message": "1086", "line": 67, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 67, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1098", "line": 67, "column": 58, "nodeType": "1074", "messageId": "1075", "endLine": 67, "endColumn": 73}, {"ruleId": "1072", "severity": 1, "message": "1129", "line": 68, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 68, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1332", "line": 69, "column": 23, "nodeType": "1074", "messageId": "1075", "endLine": 69, "endColumn": 33}, {"ruleId": "1072", "severity": 1, "message": "1107", "line": 78, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 78, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1333", "line": 79, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 79, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1334", "line": 115, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 115, "endColumn": 28}, {"ruleId": "1099", "severity": 1, "message": "1108", "line": 158, "column": 8, "nodeType": "1101", "endLine": 158, "endColumn": 10, "suggestions": "1335"}, {"ruleId": "1099", "severity": 1, "message": "1336", "line": 177, "column": 8, "nodeType": "1101", "endLine": 177, "endColumn": 16, "suggestions": "1337"}, {"ruleId": "1072", "severity": 1, "message": "1338", "line": 181, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 181, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1339", "line": 192, "column": 23, "nodeType": "1074", "messageId": "1075", "endLine": 192, "endColumn": 35}, {"ruleId": "1148", "severity": 1, "message": "1289", "line": 195, "column": 28, "nodeType": "1150", "messageId": "1151", "endLine": 195, "endColumn": 30}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 209, "column": 49, "nodeType": "1150", "messageId": "1151", "endLine": 209, "endColumn": 51}, {"ruleId": "1099", "severity": 1, "message": "1340", "line": 236, "column": 8, "nodeType": "1101", "endLine": 236, "endColumn": 10, "suggestions": "1341"}, {"ruleId": "1072", "severity": 1, "message": "1342", "line": 17, "column": 68, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 86}, {"ruleId": "1072", "severity": 1, "message": "1343", "line": 17, "column": 88, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 97}, {"ruleId": "1072", "severity": 1, "message": "1344", "line": 17, "column": 99, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 111}, {"ruleId": "1099", "severity": 1, "message": "1345", "line": 61, "column": 8, "nodeType": "1101", "endLine": 61, "endColumn": 16, "suggestions": "1346"}, {"ruleId": "1099", "severity": 1, "message": "1192", "line": 87, "column": 8, "nodeType": "1101", "endLine": 87, "endColumn": 23, "suggestions": "1347"}, {"ruleId": "1072", "severity": 1, "message": "1348", "line": 5, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1349", "line": 8, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 21}, {"ruleId": "1072", "severity": 1, "message": "1350", "line": 12, "column": 5, "nodeType": "1074", "messageId": "1075", "endLine": 12, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1351", "line": 22, "column": 5, "nodeType": "1074", "messageId": "1075", "endLine": 22, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1210", "line": 41, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 41, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1352", "line": 43, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 43, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1334", "line": 64, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 64, "endColumn": 28}, {"ruleId": "1099", "severity": 1, "message": "1353", "line": 118, "column": 8, "nodeType": "1101", "endLine": 118, "endColumn": 22, "suggestions": "1354"}, {"ruleId": "1072", "severity": 1, "message": "1355", "line": 18, "column": 27, "nodeType": "1074", "messageId": "1075", "endLine": 18, "endColumn": 40}, {"ruleId": "1072", "severity": 1, "message": "1330", "line": 18, "column": 76, "nodeType": "1074", "messageId": "1075", "endLine": 18, "endColumn": 89}, {"ruleId": "1072", "severity": 1, "message": "1356", "line": 19, "column": 63, "nodeType": "1074", "messageId": "1075", "endLine": 19, "endColumn": 81}, {"ruleId": "1099", "severity": 1, "message": "1108", "line": 56, "column": 8, "nodeType": "1101", "endLine": 56, "endColumn": 15, "suggestions": "1357"}, {"ruleId": "1099", "severity": 1, "message": "1358", "line": 64, "column": 8, "nodeType": "1101", "endLine": 64, "endColumn": 15, "suggestions": "1359"}, {"ruleId": "1099", "severity": 1, "message": "1358", "line": 75, "column": 8, "nodeType": "1101", "endLine": 75, "endColumn": 23, "suggestions": "1360"}, {"ruleId": "1099", "severity": 1, "message": "1345", "line": 89, "column": 8, "nodeType": "1101", "endLine": 89, "endColumn": 16, "suggestions": "1361"}, {"ruleId": "1072", "severity": 1, "message": "1362", "line": 6, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1363", "line": 6, "column": 64, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 81}, {"ruleId": "1072", "severity": 1, "message": "1210", "line": 6, "column": 83, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 103}, {"ruleId": "1072", "severity": 1, "message": "1364", "line": 7, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 7, "endColumn": 26}, {"ruleId": "1072", "severity": 1, "message": "1365", "line": 1, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1156", "line": 1, "column": 21, "nodeType": "1074", "messageId": "1075", "endLine": 1, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1366", "line": 9, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 9, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1090", "line": 9, "column": 26, "nodeType": "1074", "messageId": "1075", "endLine": 9, "endColumn": 50}, {"ruleId": "1072", "severity": 1, "message": "1349", "line": 11, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 11, "endColumn": 21}, {"ruleId": "1072", "severity": 1, "message": "1367", "line": 30, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 30, "endColumn": 23}, {"ruleId": "1072", "severity": 1, "message": "1216", "line": 41, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 41, "endColumn": 16}, {"ruleId": "1072", "severity": 1, "message": "1209", "line": 46, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 46, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1368", "line": 46, "column": 26, "nodeType": "1074", "messageId": "1075", "endLine": 46, "endColumn": 41}, {"ruleId": "1072", "severity": 1, "message": "1369", "line": 48, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 48, "endColumn": 28}, {"ruleId": "1072", "severity": 1, "message": "1164", "line": 53, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 53, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1098", "line": 53, "column": 26, "nodeType": "1074", "messageId": "1075", "endLine": 53, "endColumn": 41}, {"ruleId": "1072", "severity": 1, "message": "1217", "line": 57, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 57, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1370", "line": 5, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 28}, {"ruleId": "1072", "severity": 1, "message": "1371", "line": 5, "column": 30, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 43}, {"ruleId": "1072", "severity": 1, "message": "1152", "line": 8, "column": 21, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1275", "line": 19, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 19, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1372", "line": 59, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 59, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1258", "line": 66, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1373", "line": 73, "column": 12, "nodeType": "1074", "messageId": "1075", "endLine": 73, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1374", "line": 80, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 80, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1375", "line": 22, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 22, "endColumn": 21}, {"ruleId": "1099", "severity": 1, "message": "1376", "line": 86, "column": 8, "nodeType": "1101", "endLine": 86, "endColumn": 56, "suggestions": "1377"}, {"ruleId": "1072", "severity": 1, "message": "1370", "line": 5, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 28}, {"ruleId": "1072", "severity": 1, "message": "1152", "line": 8, "column": 21, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1378", "line": 8, "column": 31, "nodeType": "1074", "messageId": "1075", "endLine": 8, "endColumn": 41}, {"ruleId": "1072", "severity": 1, "message": "1175", "line": 9, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 9, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1379", "line": 10, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 10, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1380", "line": 10, "column": 26, "nodeType": "1074", "messageId": "1075", "endLine": 10, "endColumn": 39}, {"ruleId": "1072", "severity": 1, "message": "1381", "line": 10, "column": 41, "nodeType": "1074", "messageId": "1075", "endLine": 10, "endColumn": 55}, {"ruleId": "1072", "severity": 1, "message": "1382", "line": 10, "column": 57, "nodeType": "1074", "messageId": "1075", "endLine": 10, "endColumn": 69}, {"ruleId": "1072", "severity": 1, "message": "1315", "line": 11, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 11, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1383", "line": 12, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 12, "endColumn": 22}, {"ruleId": "1072", "severity": 1, "message": "1384", "line": 13, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 13, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1314", "line": 14, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 14, "endColumn": 22}, {"ruleId": "1072", "severity": 1, "message": "1176", "line": 16, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 16, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1177", "line": 49, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 49, "endColumn": 27}, {"ruleId": "1072", "severity": 1, "message": "1178", "line": 55, "column": 7, "nodeType": "1074", "messageId": "1075", "endLine": 55, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1323", "line": 71, "column": 5, "nodeType": "1074", "messageId": "1075", "endLine": 71, "endColumn": 12}, {"ruleId": "1072", "severity": 1, "message": "1372", "line": 77, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 77, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1385", "line": 81, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 81, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1386", "line": 81, "column": 21, "nodeType": "1074", "messageId": "1075", "endLine": 81, "endColumn": 33}, {"ruleId": "1072", "severity": 1, "message": "1387", "line": 83, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 83, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1163", "line": 83, "column": 22, "nodeType": "1074", "messageId": "1075", "endLine": 83, "endColumn": 35}, {"ruleId": "1072", "severity": 1, "message": "1388", "line": 85, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 85, "endColumn": 21}, {"ruleId": "1072", "severity": 1, "message": "1389", "line": 85, "column": 23, "nodeType": "1074", "messageId": "1075", "endLine": 85, "endColumn": 37}, {"ruleId": "1072", "severity": 1, "message": "1390", "line": 87, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 87, "endColumn": 26}, {"ruleId": "1072", "severity": 1, "message": "1164", "line": 88, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 88, "endColumn": 22}, {"ruleId": "1072", "severity": 1, "message": "1098", "line": 88, "column": 24, "nodeType": "1074", "messageId": "1075", "endLine": 88, "endColumn": 39}, {"ruleId": "1072", "severity": 1, "message": "1391", "line": 89, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 89, "endColumn": 27}, {"ruleId": "1072", "severity": 1, "message": "1392", "line": 89, "column": 29, "nodeType": "1074", "messageId": "1075", "endLine": 89, "endColumn": 49}, {"ruleId": "1072", "severity": 1, "message": "1393", "line": 90, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 90, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1394", "line": 90, "column": 31, "nodeType": "1074", "messageId": "1075", "endLine": 90, "endColumn": 53}, {"ruleId": "1072", "severity": 1, "message": "1328", "line": 91, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 91, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1395", "line": 91, "column": 20, "nodeType": "1074", "messageId": "1075", "endLine": 91, "endColumn": 31}, {"ruleId": "1072", "severity": 1, "message": "1396", "line": 92, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 92, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1107", "line": 96, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 96, "endColumn": 23}, {"ruleId": "1072", "severity": 1, "message": "1374", "line": 99, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 99, "endColumn": 23}, {"ruleId": "1072", "severity": 1, "message": "1397", "line": 228, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 228, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1398", "line": 234, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 234, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1399", "line": 240, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 240, "endColumn": 27}, {"ruleId": "1072", "severity": 1, "message": "1375", "line": 20, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 20, "endColumn": 25}, {"ruleId": "1072", "severity": 1, "message": "1400", "line": 21, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 21, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1375", "line": 39, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 39, "endColumn": 21}, {"ruleId": "1099", "severity": 1, "message": "1376", "line": 120, "column": 8, "nodeType": "1101", "endLine": 120, "endColumn": 56, "suggestions": "1401"}, {"ruleId": "1072", "severity": 1, "message": "1375", "line": 22, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 22, "endColumn": 21}, {"ruleId": "1072", "severity": 1, "message": "1129", "line": 34, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 34, "endColumn": 20}, {"ruleId": "1099", "severity": 1, "message": "1376", "line": 84, "column": 8, "nodeType": "1101", "endLine": 84, "endColumn": 56, "suggestions": "1402"}, {"ruleId": "1072", "severity": 1, "message": "1403", "line": 6, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1123", "line": 11, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 11, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1375", "line": 26, "column": 5, "nodeType": "1074", "messageId": "1075", "endLine": 26, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1129", "line": 43, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 43, "endColumn": 18}, {"ruleId": "1099", "severity": 1, "message": "1376", "line": 112, "column": 6, "nodeType": "1101", "endLine": 112, "endColumn": 54, "suggestions": "1404"}, {"ruleId": "1099", "severity": 1, "message": "1405", "line": 103, "column": 6, "nodeType": "1101", "endLine": 103, "endColumn": 24, "suggestions": "1406"}, {"ruleId": "1072", "severity": 1, "message": "1407", "line": 6, "column": 3, "nodeType": "1074", "messageId": "1075", "endLine": 6, "endColumn": 23}, {"ruleId": "1072", "severity": 1, "message": "1408", "line": 10, "column": 3, "nodeType": "1074", "messageId": "1075", "endLine": 10, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1409", "line": 5, "column": 3, "nodeType": "1074", "messageId": "1075", "endLine": 5, "endColumn": 6}, {"ruleId": "1072", "severity": 1, "message": "1410", "line": 9, "column": 3, "nodeType": "1074", "messageId": "1075", "endLine": 9, "endColumn": 7}, {"ruleId": "1072", "severity": 1, "message": "1411", "line": 11, "column": 3, "nodeType": "1074", "messageId": "1075", "endLine": 11, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1111", "line": 2, "column": 49, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 55}, {"ruleId": "1072", "severity": 1, "message": "1317", "line": 23, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 23, "endColumn": 19}, {"ruleId": "1072", "severity": 1, "message": "1318", "line": 25, "column": 8, "nodeType": "1074", "messageId": "1075", "endLine": 25, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1412", "line": 43, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 43, "endColumn": 15}, {"ruleId": "1072", "severity": 1, "message": "1413", "line": 46, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 46, "endColumn": 18}, {"ruleId": "1072", "severity": 1, "message": "1400", "line": 50, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 50, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1216", "line": 56, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 56, "endColumn": 16}, {"ruleId": "1072", "severity": 1, "message": "1414", "line": 65, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 65, "endColumn": 21}, {"ruleId": "1072", "severity": 1, "message": "1415", "line": 66, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 66, "endColumn": 35}, {"ruleId": "1072", "severity": 1, "message": "1374", "line": 71, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 71, "endColumn": 21}, {"ruleId": "1072", "severity": 1, "message": "1416", "line": 147, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 147, "endColumn": 29}, {"ruleId": "1072", "severity": 1, "message": "1417", "line": 177, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 177, "endColumn": 26}, {"ruleId": "1072", "severity": 1, "message": "1281", "line": 19, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 19, "endColumn": 20}, {"ruleId": "1072", "severity": 1, "message": "1418", "line": 9, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 9, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1419", "line": 9, "column": 19, "nodeType": "1074", "messageId": "1075", "endLine": 9, "endColumn": 26}, {"ruleId": "1072", "severity": 1, "message": "1420", "line": 9, "column": 28, "nodeType": "1074", "messageId": "1075", "endLine": 9, "endColumn": 37}, {"ruleId": "1072", "severity": 1, "message": "1323", "line": 21, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 21, "endColumn": 16}, {"ruleId": "1072", "severity": 1, "message": "1421", "line": 37, "column": 13, "nodeType": "1074", "messageId": "1075", "endLine": 37, "endColumn": 31}, {"ruleId": "1072", "severity": 1, "message": "1422", "line": 108, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 108, "endColumn": 24}, {"ruleId": "1072", "severity": 1, "message": "1110", "line": 2, "column": 38, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 47}, {"ruleId": "1072", "severity": 1, "message": "1216", "line": 17, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 17, "endColumn": 14}, {"ruleId": "1072", "severity": 1, "message": "1412", "line": 23, "column": 11, "nodeType": "1074", "messageId": "1075", "endLine": 23, "endColumn": 17}, {"ruleId": "1072", "severity": 1, "message": "1323", "line": 23, "column": 19, "nodeType": "1074", "messageId": "1075", "endLine": 23, "endColumn": 26}, {"ruleId": "1072", "severity": 1, "message": "1111", "line": 2, "column": 49, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 55}, {"ruleId": "1072", "severity": 1, "message": "1112", "line": 2, "column": 57, "nodeType": "1074", "messageId": "1075", "endLine": 2, "endColumn": 68}, {"ruleId": "1072", "severity": 1, "message": "1157", "line": 10, "column": 10, "nodeType": "1074", "messageId": "1075", "endLine": 10, "endColumn": 23}, {"ruleId": "1072", "severity": 1, "message": "1135", "line": 25, "column": 9, "nodeType": "1074", "messageId": "1075", "endLine": 25, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'useAuth' is defined but never used.", "'getAuth' is defined but never used.", "'signInWithEmailAndPassword' is defined but never used.", "'signOut' is defined but never used.", "'onAuthStateChanged' is defined but never used.", "'axiosInstance' is assigned a value but never used.", "'setAxiosInstance' is assigned a value but never used.", "'user' is assigned a value but never used.", "'setUser' is assigned a value but never used.", "'saveToken' is assigned a value but never used.", "'playerScores' is assigned a value but never used.", "'setPlayerScores' is assigned a value but never used.", "'sendGridToPlayers' is defined but never used.", "'setCurrentChunk' is defined but never used.", "'setCurrentPacketQuestion' is defined but never used.", "'round' is defined but never used.", "'setSelectedPacketToPlayer' is defined but never used.", "'testId' is assigned a value but never used.", "'sounds' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'timeLeft' is assigned a value but never used.", "'setTimeLeft' is assigned a value but never used.", "'setAnimationKey' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'hostRoomId'. Either include it or remove the dependency array.", "ArrayExpression", ["1423"], "'deletePath' is defined but never used.", "'listenToTimeStart' is defined but never used.", "'useHost' is defined but never used.", "'roundRef' is assigned a value but never used.", "'isInitialMount' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'roomId'. Either include it or remove the dependency array.", ["1424"], "'ReactNode' is defined but never used.", "'useRef' is defined but never used.", "'useCallback' is defined but never used.", "React Hook useEffect has missing dependencies: 'roomId' and 'testName'. Either include them or remove the dependency array.", ["1425"], "React Hook useEffect has missing dependencies: 'roomId' and 'sounds'. Either include them or remove the dependency array.", ["1426"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'isAllowed' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isSpectator', 'navigate', 'round', and 'setInitialGrid'. Either include them or remove the dependency array.", ["1427"], "'ReactPlaceholder' is defined but never used.", "'listenToBuzzing' is defined but never used.", "'buzzedPlayer' is assigned a value but never used.", "'setBuzzedPlayer' is assigned a value but never used.", "'showModal' is assigned a value but never used.", "'setShowModal' is assigned a value but never used.", "'isMounted' is assigned a value but never used.", ["1428"], ["1429"], "'joinRoom' is defined but never used.", "'Round4' is defined but never used.", "'exampleGrid' is assigned a value but never used.", "'loading' is assigned a value but never used.", ["1430"], "React Hook useEffect has a missing dependency: 'images.length'. Either include it or remove the dependency array.", ["1431"], ["1432"], "'banner' is defined but never used.", "'card' is defined but never used.", "'Header' is defined but never used.", "'Round1' is defined but never used.", "'QuestionBoxRound1' is defined but never used.", "'Play' is defined but never used.", ["1433"], "'getToken' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'Question' is defined but never used.", "'getAxiosAuthContext' is defined but never used.", "'Players' is defined but never used.", "'lastStartTime' is assigned a value but never used.", "'useState' is defined but never used.", "'updateHistory' is defined but never used.", "'authenticateUser' is defined but never used.", "'errorCode' is assigned a value but never used.", "'notifyBackendFileUploaded' is defined but never used.", "'RoundBase' is defined but never used.", "'set' is defined but never used.", "'setIsExpanded' is assigned a value but never used.", "'animationKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'playerAnswerRef'. Either include it or remove the dependency array. Mutable values like 'playerAnswerRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["1434"], "React Hook useEffect has missing dependencies: 'roomId' and 'startTimer'. Either include them or remove the dependency array.", ["1435"], "React Hook useEffect has missing dependencies: 'currentPlayerAvatar', 'currentPlayerName', 'isHost', 'isSpectator', 'playerAnswerRef', 'playerAnswerTime', 'position', 'roomId', and 'setAnimationKey'. Either include them or remove the dependency array.", ["1436"], "React Hook useEffect has missing dependencies: 'currentAnswer', 'isHost', 'roomId', and 'setAnswerList'. Either include them or remove the dependency array.", ["1437"], ["1438"], ["1439"], "'renderGrid' is defined but never used.", "'HintWord' is defined but never used.", "'QuestionBoxProps' is defined but never used.", "'mainKeyword' is assigned a value but never used.", "'hintWordsLength' is assigned a value but never used.", "'setHintWordsLength' is assigned a value but never used.", "'markedCharacters' is assigned a value but never used.", "'highlightedCharacters' is assigned a value but never used.", ["1440"], ["1441"], "'topBound' is assigned a value but never used.", "'bottomBound' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a return value from arrow function.", "ArrowFunctionExpression", "expectedInside", "'hasMounted' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'sounds'. Either include it or remove the dependency array.", ["1442"], ["1443"], "React Hook useEffect has missing dependencies: 'isHost' and 'setInitialGrid'. Either include them or remove the dependency array.", ["1444"], "React Hook useEffect has a missing dependency: 'revealCellsForPlayer'. Either include it or remove the dependency array.", ["1445"], "React Hook useEffect has missing dependencies: 'roomId' and 'setAnswerList'. Either include them or remove the dependency array.", ["1446"], "React Hook useEffect has missing dependencies: 'revealCellsForPlayer' and 'setAnswerList'. Either include them or remove the dependency array.", ["1447"], "React Hook useEffect has missing dependencies: 'revealCellsForPlayer' and 'sounds'. Either include them or remove the dependency array.", ["1448"], ["1449"], "'listenToCurrentQuestionsNumber' is defined but never used.", "'sendCorrectAnswer' is defined but never used.", "'MAX_PACKET_QUESTION' is assigned a value but never used.", "'hiddenTopics' is assigned a value but never used.", "'currentQuestionIndex' is assigned a value but never used.", "'inGameQuestionIndex' is assigned a value but never used.", "'playerCurrentQuestionIndex' is assigned a value but never used.", "'tempQuestionListRef' is assigned a value but never used.", "'tempQuestionList' is assigned a value but never used.", "'setTempQuestionList' is assigned a value but never used.", "'round' is assigned a value but never used.", "'isFirstMounted' is assigned a value but never used.", "'decodeQuestion' is defined but never used.", ["1450"], "'timeOut' is assigned a value but never used.", ["1451"], "React Hook useEffect has missing dependencies: 'setCurrentQuestion' and 'setSelectedTopic'. Either include them or remove the dependency array.", ["1452"], ["1453"], "React Hook useEffect has a missing dependency: 'setAnimationKey'. Either include it or remove the dependency array.", ["1454"], "React Hook useEffect has missing dependencies: 'isHost', 'roomId', and 'testName'. Either include them or remove the dependency array.", ["1455"], ["1456"], "React Hook useEffect has missing dependencies: 'isHost', 'roomId', and 'setSelectedTopic'. Either include them or remove the dependency array.", ["1457"], "React Hook useEffect has missing dependencies: 'currentAnswer', 'isHost', 'roomId', 'setAnswerList', and 'setCurrentQuestion'. Either include them or remove the dependency array.", ["1458"], "'uploadTestToServer' is defined but never used.", "'response' is assigned a value but never used.", "'getTest' is defined but never used.", "'getTestByUserId' is defined but never used.", "'addNewQuestion' is defined but never used.", "'useQuery' is defined but never used.", "'handleEditClick' is assigned a value but never used.", "'handleAddQuestion' is assigned a value but never used.", ["1459"], ["1460"], ["1461"], ["1462"], ["1463"], ["1464"], "'listenToSound' is defined but never used.", "'listenToQuestions' is defined but never used.", "'listenToSelectedCell' is defined but never used.", "'listenToCellColor' is defined but never used.", "'listenToAnswers' is defined but never used.", "'listenToStar' is defined but never used.", "'colorMap' is assigned a value but never used.", "'selectedQuestion' is assigned a value but never used.", "'setSelectedQuestion' is assigned a value but never used.", "'setMenu' is assigned a value but never used.", "'selectedCell' is assigned a value but never used.", "'setEasyQuestionNumber' is assigned a value but never used.", "'setMediumQuestionNumber' is assigned a value but never used.", "'setHardQuestionNumber' is assigned a value but never used.", "'setLevel' is assigned a value but never used.", ["1465"], ["1466"], "'updateQuestion' is defined but never used.", "'uploadFile' is defined but never used.", "'testManageMentService' is defined but never used.", "'RoomScoreTableProps' is defined but never used.", ["1467"], ["1468"], ["1469"], ["1470"], ["1471"], ["1472"], "'GameGridProps' is defined but never used.", "'exampleQuestions' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'initialGrid' and 'setInitialGrid'. Either include them or remove the dependency array. If 'setGrid' needs the current value of 'initialGrid', you can also switch to useReducer instead of useState and read 'initialGrid' in the reducer.", ["1473"], ["1474"], "'lastBuzzedPlayerRef' is assigned a value but never used.", "'prevOrder' is assigned a value but never used.", "'uploadedFile' is assigned a value but never used.", "'CleanVars' is defined but never used.", "'curMatch' is defined but never used.", "@typescript-eslint/no-redeclare", "'xIndex' is already defined.", "redeclared", "'yIndex' is already defined.", "Expected '!==' and instead saw '!='.", "React Hook useEffect has missing dependencies: 'roomId' and 'startTimer'. Either include them or remove the dependency array. If 'startTimer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1475"], "React Hook useEffect has missing dependencies: 'setBuzzedPlayer', 'setShowModal', and 'sounds'. Either include them or remove the dependency array. If 'setBuzzedPlayer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1476"], "React Hook useEffect has missing dependencies: 'setShowModal', 'setStaredPlayer', and 'sounds'. Either include them or remove the dependency array. If 'setStaredPlayer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1477"], ["1478"], "React Hook useEffect has missing dependencies: 'roomId', 'setCorrectAnswer', and 'sounds'. Either include them or remove the dependency array. If 'setCorrectAnswer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1479"], "React Hook useEffect has missing dependencies: 'roomId', 'setCorrectAnswer', and 'setCurrentQuestion'. Either include them or remove the dependency array. If 'setCurrentQuestion' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1480"], "React Hook useEffect has missing dependencies: 'roomId', 'setGridColors', and 'setSelectedCell'. Either include them or remove the dependency array. If 'setGridColors' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1481"], "React Hook useEffect has missing dependencies: 'colorMap', 'roomId', 'setGrid', and 'setGridColors'. Either include them or remove the dependency array. If 'setGridColors' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1482"], "React Hook useEffect has a missing dependency: 'playerAnswerRef'. Either include it or remove the dependency array.", ["1483"], "'Answer' is defined but never used.", "'User' is defined but never used.", "'addPlayerToRoom' is defined but never used.", "'listenToRules' is defined but never used.", "'listenToScores' is defined but never used.", "'listenToBroadcastedAnswer' is defined but never used.", "'listenToRoundStart' is defined but never used.", "'submitAnswer' is defined but never used.", "'getNextQuestion' is defined but never used.", "'HostManagement' is defined but never used.", "'PlayerScore' is defined but never used.", "'HostScore' is defined but never used.", "'EyeIcon' is defined but never used.", "'Player' is defined but never used.", "'playerAnswerRef' is assigned a value but never used.", "'setUserId' is assigned a value but never used.", "'players' is assigned a value but never used.", "'setPlayers' is assigned a value but never used.", "'setRoomId' is assigned a value but never used.", "'playersArray' is assigned a value but never used.", "'roomRules' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setCurrentQuestion' is assigned a value but never used.", "'selectedTopic' is assigned a value but never used.", "'setSelectedTopic' is assigned a value but never used.", "'startTimer' is assigned a value but never used.", "'styles' is assigned a value but never used.", "'handleRoundChange' is assigned a value but never used.", ["1484"], "React Hook useEffect has a missing dependency: 'setRoomRules'. Either include it or remove the dependency array.", ["1485"], "'isFull' is assigned a value but never used.", "'scoreInitKey' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'roomId', 'setPlayerArray', 'setPlayerScores', and 'setScoreList'. Either include them or remove the dependency array.", ["1486"], "'triggerPlayerFlash' is assigned a value but never used.", "'scoreList' is assigned a value but never used.", "'setScoreList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setAnswerList'. Either include it or remove the dependency array.", ["1487"], ["1488"], "'openBuzz' is defined but never used.", "'updateScore' is defined but never used.", "'BellAlertIcon' is defined but never used.", "'PaintBrushIcon' is defined but never used.", "'hostInitialGrid' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'roomId' and 'setInGameQuestionIndex'. Either include them or remove the dependency array.", ["1489"], "'playerFlashes' is assigned a value but never used.", "'handleNextQuestion' is assigned a value but never used.", ["1490"], "React Hook useEffect has a missing dependency: 'setPlayerScores'. Either include it or remove the dependency array.", ["1491"], ["1492"], ["1493"], "'currentAnswer' is assigned a value but never used.", "'showCurrentAnswer' is assigned a value but never used.", "'currentQuestion' is assigned a value but never used.", "'authService' is defined but never used.", "'getPacketNames' is defined but never used.", "'questionNumber' is assigned a value but never used.", "'setHiddenTopics' is assigned a value but never used.", "'showReturnButton' is assigned a value but never used.", "'setCurrentQuestion' is defined but never used.", "'setRound4Grid' is defined but never used.", "'testName' is assigned a value but never used.", "'starPositions' is assigned a value but never used.", "'getQuestions' is assigned a value but never used.", "'currentRound' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'listenToGameState'. Either include it or remove the dependency array.", ["1494"], "'Round2Grid' is defined but never used.", "'setSelectedRow' is defined but never used.", "'setCorrectRow' is defined but never used.", "'setIncorectRow' is defined but never used.", "'openObstacle' is defined but never used.", "'generateGrid' is defined but never used.", "'PlayerAnswerInput' is defined but never used.", "'hintWords' is assigned a value but never used.", "'setHintWords' is assigned a value but never used.", "'isExpanded' is assigned a value but never used.", "'isModalOpen' is assigned a value but never used.", "'setIsModalOpen' is assigned a value but never used.", "'playerAnswerTime' is assigned a value but never used.", "'currentPlayerName' is assigned a value but never used.", "'setCurrentPlayerName' is assigned a value but never used.", "'currentPlayerAvatar' is assigned a value but never used.", "'setCurrentPlayerAvatar' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'answerList' is assigned a value but never used.", "'handleRowSelect' is assigned a value but never used.", "'handleRowCorrect' is assigned a value but never used.", "'handleRowIncorrect' is assigned a value but never used.", "'currentRoom' is assigned a value but never used.", ["1495"], ["1496"], "'addToast' is defined but never used.", ["1497"], "React Hook useCallback has an unnecessary dependency: 'dispatch'. Either exclude it or remove the dependency array.", ["1498"], "'GetQuestionsResponse' is defined but never used.", "'ScoringResponse' is defined but never used.", "'off' is defined but never used.", "'push' is defined but never used.", "'DatabaseReference' is defined but never used.", "'scores' is assigned a value but never used.", "'showRules' is assigned a value but never used.", "'updatePlayer' is assigned a value but never used.", "'setCurrentQuestionFirebase' is assigned a value but never used.", "'handleSubmitAnswer' is assigned a value but never used.", "'handleShowRules' is assigned a value but never used.", "'buzzing' is defined but never used.", "'setStar' is defined but never used.", "'closeBuzz' is defined but never used.", "'submitPlayerAnswer' is assigned a value but never used.", "'handleSetStar' is assigned a value but never used.", {"desc": "1499", "fix": "1500"}, {"desc": "1501", "fix": "1502"}, {"desc": "1503", "fix": "1504"}, {"desc": "1505", "fix": "1506"}, {"desc": "1507", "fix": "1508"}, {"desc": "1507", "fix": "1509"}, {"desc": "1507", "fix": "1510"}, {"desc": "1507", "fix": "1511"}, {"desc": "1512", "fix": "1513"}, {"desc": "1507", "fix": "1514"}, {"desc": "1503", "fix": "1515"}, {"desc": "1516", "fix": "1517"}, {"desc": "1518", "fix": "1519"}, {"desc": "1520", "fix": "1521"}, {"desc": "1522", "fix": "1523"}, {"desc": "1505", "fix": "1524"}, {"desc": "1505", "fix": "1525"}, {"desc": "1520", "fix": "1526"}, {"desc": "1518", "fix": "1527"}, {"desc": "1505", "fix": "1528"}, {"desc": "1505", "fix": "1529"}, {"desc": "1530", "fix": "1531"}, {"desc": "1532", "fix": "1533"}, {"desc": "1534", "fix": "1535"}, {"desc": "1536", "fix": "1537"}, {"desc": "1538", "fix": "1539"}, {"desc": "1538", "fix": "1540"}, {"desc": "1505", "fix": "1541"}, {"desc": "1542", "fix": "1543"}, {"desc": "1544", "fix": "1545"}, {"desc": "1518", "fix": "1546"}, {"desc": "1547", "fix": "1548"}, {"desc": "1549", "fix": "1550"}, {"desc": "1542", "fix": "1551"}, {"desc": "1552", "fix": "1553"}, {"desc": "1554", "fix": "1555"}, {"desc": "1516", "fix": "1556"}, {"desc": "1518", "fix": "1557"}, {"desc": "1520", "fix": "1558"}, {"desc": "1522", "fix": "1559"}, {"desc": "1505", "fix": "1560"}, {"desc": "1505", "fix": "1561"}, {"desc": "1547", "fix": "1562"}, {"desc": "1518", "fix": "1563"}, {"desc": "1516", "fix": "1564"}, {"desc": "1518", "fix": "1565"}, {"desc": "1520", "fix": "1566"}, {"desc": "1522", "fix": "1567"}, {"desc": "1505", "fix": "1568"}, {"desc": "1505", "fix": "1569"}, {"desc": "1570", "fix": "1571"}, {"desc": "1547", "fix": "1572"}, {"desc": "1518", "fix": "1573"}, {"desc": "1574", "fix": "1575"}, {"desc": "1576", "fix": "1577"}, {"desc": "1505", "fix": "1578"}, {"desc": "1579", "fix": "1580"}, {"desc": "1581", "fix": "1582"}, {"desc": "1583", "fix": "1584"}, {"desc": "1585", "fix": "1586"}, {"desc": "1587", "fix": "1588"}, {"desc": "1542", "fix": "1589"}, {"desc": "1590", "fix": "1591"}, {"desc": "1592", "fix": "1593"}, {"desc": "1534", "fix": "1594"}, {"desc": "1595", "fix": "1596"}, {"desc": "1597", "fix": "1598"}, {"desc": "1599", "fix": "1600"}, {"desc": "1601", "fix": "1602"}, {"desc": "1603", "fix": "1604"}, {"desc": "1534", "fix": "1605"}, {"desc": "1606", "fix": "1607"}, {"desc": "1606", "fix": "1608"}, {"desc": "1606", "fix": "1609"}, {"desc": "1606", "fix": "1610"}, {"desc": "1542", "fix": "1611"}, "Update the dependencies array to be: [hostRoomId]", {"range": "1612", "text": "1613"}, "Update the dependencies array to be: [location.pathname, requireAccessToken, requireHost, roomId]", {"range": "1614", "text": "1615"}, "Update the dependencies array to be: [roomId, testName]", {"range": "1616", "text": "1617"}, "Update the dependencies array to be: [roomId, sounds]", {"range": "1618", "text": "1619"}, "Update the dependencies array to be: [isSpectator, navigate, roomId, round, setInitialGrid]", {"range": "1620", "text": "1621"}, {"range": "1622", "text": "1621"}, {"range": "1623", "text": "1621"}, {"range": "1624", "text": "1621"}, "Update the dependencies array to be: [images.length]", {"range": "1625", "text": "1626"}, {"range": "1627", "text": "1621"}, {"range": "1628", "text": "1617"}, "Update the dependencies array to be: [playerAnswerRef]", {"range": "1629", "text": "1630"}, "Update the dependencies array to be: [roomId, startTimer]", {"range": "1631", "text": "1632"}, "Update the dependencies array to be: [currentPlayerAvatar, currentPlayerName, isHost, isSpectator, playerAnswerRef, playerAnswerTime, position, roomId, setAnimationKey, timeLeft]", {"range": "1633", "text": "1634"}, "Update the dependencies array to be: [currentAnswer, isHost, roomId, setAnswerList]", {"range": "1635", "text": "1636"}, {"range": "1637", "text": "1619"}, {"range": "1638", "text": "1619"}, {"range": "1639", "text": "1634"}, {"range": "1640", "text": "1632"}, {"range": "1641", "text": "1619"}, {"range": "1642", "text": "1619"}, "Update the dependencies array to be: [hintWordArray, obstacleWord, initialGrid, isHost, setInitialGrid]", {"range": "1643", "text": "1644"}, "Update the dependencies array to be: [roomId, grid, revealCellsForPlayer]", {"range": "1645", "text": "1646"}, "Update the dependencies array to be: [roomId, setAnswerList]", {"range": "1647", "text": "1648"}, "Update the dependencies array to be: [roomId, grid, setAnswerList, revealCellsForPlayer]", {"range": "1649", "text": "1650"}, "Update the dependencies array to be: [roomId, grid, sounds, revealCellsForPlayer]", {"range": "1651", "text": "1652"}, {"range": "1653", "text": "1652"}, {"range": "1654", "text": "1619"}, "Update the dependencies array to be: [roomId]", {"range": "1655", "text": "1656"}, "Update the dependencies array to be: [roomId, isHost, setCurrentQuestion, setSelectedTopic]", {"range": "1657", "text": "1658"}, {"range": "1659", "text": "1632"}, "Update the dependencies array to be: [setAnimationKey, timeLeft]", {"range": "1660", "text": "1661"}, "Update the dependencies array to be: [isHost, roomId, testName]", {"range": "1662", "text": "1663"}, {"range": "1664", "text": "1656"}, "Update the dependencies array to be: [isHost, roomId, setSelectedTopic]", {"range": "1665", "text": "1666"}, "Update the dependencies array to be: [currentAnswer, isHost, roomId, setAnswerList, setCurrentQuestion]", {"range": "1667", "text": "1668"}, {"range": "1669", "text": "1630"}, {"range": "1670", "text": "1632"}, {"range": "1671", "text": "1634"}, {"range": "1672", "text": "1636"}, {"range": "1673", "text": "1619"}, {"range": "1674", "text": "1619"}, {"range": "1675", "text": "1661"}, {"range": "1676", "text": "1632"}, {"range": "1677", "text": "1630"}, {"range": "1678", "text": "1632"}, {"range": "1679", "text": "1634"}, {"range": "1680", "text": "1636"}, {"range": "1681", "text": "1619"}, {"range": "1682", "text": "1619"}, "Update the dependencies array to be: [initialGrid, setInitialGrid]", {"range": "1683", "text": "1684"}, {"range": "1685", "text": "1661"}, {"range": "1686", "text": "1632"}, "Update the dependencies array to be: [roomId, setBuzzedPlayer, setShowModal, sounds]", {"range": "1687", "text": "1688"}, "Update the dependencies array to be: [roomId, setShowModal, setStaredPlayer, sounds]", {"range": "1689", "text": "1690"}, {"range": "1691", "text": "1619"}, "Update the dependencies array to be: [roomId, setCorrectAnswer, sounds]", {"range": "1692", "text": "1693"}, "Update the dependencies array to be: [roomId, setCorrectAnswer, setCurrentQuestion]", {"range": "1694", "text": "1695"}, "Update the dependencies array to be: [roomId, setGridColors, setSelectedCell]", {"range": "1696", "text": "1697"}, "Update the dependencies array to be: [colorMap, roomId, setGrid, setGridColors]", {"range": "1698", "text": "1699"}, "Update the dependencies array to be: [playerAnswerRef, question]", {"range": "1700", "text": "1701"}, {"range": "1702", "text": "1656"}, "Update the dependencies array to be: [roomId, setRoomRules]", {"range": "1703", "text": "1704"}, "Update the dependencies array to be: [roomId, setPlayerArray, setPlayerScores, setScoreList]", {"range": "1705", "text": "1706"}, {"range": "1707", "text": "1648"}, "Update the dependencies array to be: [roomId, round, sounds]", {"range": "1708", "text": "1709"}, "Update the dependencies array to be: [currentRound, roomId, setInGameQuestionIndex]", {"range": "1710", "text": "1711"}, "Update the dependencies array to be: [roomId, round]", {"range": "1712", "text": "1713"}, "Update the dependencies array to be: [round, setPlayerScores]", {"range": "1714", "text": "1715"}, "Update the dependencies array to be: [round, roomId, setPlayerScores]", {"range": "1716", "text": "1717"}, {"range": "1718", "text": "1648"}, "Update the dependencies array to be: [roomId, round, isSpectator, navigate, dispatch, listenToGameState]", {"range": "1719", "text": "1720"}, {"range": "1721", "text": "1720"}, {"range": "1722", "text": "1720"}, {"range": "1723", "text": "1720"}, {"range": "1724", "text": "1656"}, [3417, 3419], "[hostRoomId]", [5323, 5375], "[location.pathname, requireAccessToken, requireHost, roomId]", [1936, 1938], "[roomId, testName]", [995, 997], "[roomId, sounds]", [2208, 2216], "[isSpectator, navigate, roomId, round, setInitialGrid]", [2436, 2444], [2276, 2284], [3055, 3063], [519, 521], "[images.length]", [2257, 2265], [1146, 1148], [1875, 1900], "[playerAnswerRef]", [2622, 2624], "[roomId, startTimer]", [3507, 3517], "[currentP<PERSON><PERSON><PERSON><PERSON>, currentPlayer<PERSON><PERSON>, isHost, isSpectator, playerAnswerRef, playerAnswerTime, position, roomId, setAnimationKey, timeLeft]", [4232, 4234], "[current<PERSON>ns<PERSON>, isHost, roomId, setAnswerList]", [4720, 4722], [5167, 5169], [4341, 4351], [4802, 4804], [7254, 7262], [7684, 7686], [10089, 10131], "[hint<PERSON>ordArray, obstacle<PERSON>ord, initialGrid, isHost, setInitialGrid]", [22416, 22430], "[roomId, grid, revealCellsForPlayer]", [22788, 22790], "[roomId, setAnswerList]", [23836, 23850], "[roomId, grid, setAnswerList, revealCellsForPlayer]", [25089, 25103], "[roomId, grid, sounds, revealCellsForPlayer]", [26651, 26665], [6173, 6175], [6699, 6701], "[roomId]", [7869, 7885], "[roomId, isHost, setCurrentQuestion, setSelectedTopic]", [8798, 8800], [9659, 9669], "[setAnimationKey, timeLeft]", [9905, 9907], "[isHost, roomId, testName]", [10225, 10227], [10814, 10816], "[isHost, roomId, setSelectedTopic]", [12101, 12103], "[current<PERSON><PERSON><PERSON>, isHost, roomId, setAnswerList, setCurrentQuestion]", [1770, 1795], [2478, 2480], [3327, 3337], [4054, 4056], [4542, 4544], [4989, 4991], [3367, 3377], [3869, 3871], [1768, 1793], [2476, 2478], [3325, 3335], [4052, 4054], [4540, 4542], [4987, 4989], [12138, 12140], "[initialGrid, setInitialGrid]", [12757, 12767], [1852, 1854], [2759, 2767], "[roomId, setBuzzedPlayer, setShowModal, sounds]", [3683, 3691], "[roomId, setShowModal, setStaredPlayer, sounds]", [4177, 4179], [4810, 4812], "[roomId, setCorrectAnswer, sounds]", [5136, 5138], "[roomId, setCorrectAnswer, setCurrentQuestion]", [7006, 7008], "[roomId, setGridColors, setSelectedCell]", [7959, 7961], "[colorMap, roomId, setGrid, setGridColors]", [1498, 1508], "[player<PERSON><PERSON><PERSON><PERSON><PERSON>, question]", [5691, 5693], [6255, 6263], "[roomId, setRoomRules]", [8892, 8894], "[roomId, setPlayerArray, setPlayerScores, setScoreList]", [2359, 2367], [3121, 3136], "[roomId, round, sounds]", [4512, 4526], "[currentRound, roomId, setInGameQuestionIndex]", [2609, 2616], "[roomId, round]", [2827, 2834], "[round, setPlayerScores]", [3162, 3177], "[round, roomId, setPlayerScores]", [3624, 3632], [3207, 3255], "[roomId, round, isSpectator, navigate, dispatch, listenToGameState]", [4402, 4450], [2885, 2933], [3558, 3606], [3073, 3091]]