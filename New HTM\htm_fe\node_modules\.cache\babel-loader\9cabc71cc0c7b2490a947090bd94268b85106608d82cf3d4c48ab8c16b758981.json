{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\FinalScore.migrated.tsx\",\n  _s = $RefreshSig$();\n// MIGRATED VERSION: FinalScore using Redux and new hooks\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { useAppSelector, useAppDispatch } from '../app/store';\nimport { addToast } from '../app/store/slices/uiSlice';\nimport { useFirebaseListener } from '../shared/hooks';\nimport PlayerScore from '../components/PlayerScore.migrated';\nimport Header from '../layouts/Header';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FinalScore = ({\n  isHost = false,\n  buttonComponent\n}) => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const dispatch = useAppDispatch();\n\n  // Redux state\n  const {\n    scores,\n    players,\n    loading\n  } = useAppSelector(state => state.game);\n\n  // URL params\n  const roomId = searchParams.get(\"roomId\") || \"\";\n  const testName = searchParams.get(\"testName\") || \"\";\n\n  // Local state\n  const [isHistoryUpdated, setIsHistoryUpdated] = useState(false);\n  const [showConfetti, setShowConfetti] = useState(false);\n\n  // Hooks\n  const {\n    listenToScores\n  } = useFirebaseListener(roomId);\n\n  // Listen to final scores\n  useEffect(() => {\n    if (!roomId) return;\n    return listenToScores(scoresData => {\n      if (scoresData) {\n        // Trigger confetti animation when scores are loaded\n        setShowConfetti(true);\n        setTimeout(() => setShowConfetti(false), 5000);\n      }\n    });\n  }, [roomId, listenToScores]);\n\n  // Update history when component mounts\n  useEffect(() => {\n    if (!roomId || !testName || isHistoryUpdated) return;\n    const updateGameHistory = async () => {\n      try {\n        // Note: updateHistory function needs to be implemented\n        console.log('Updating game history:', {\n          roomId,\n          testName\n        });\n        setIsHistoryUpdated(true);\n        dispatch(addToast({\n          type: 'success',\n          title: 'Game Complete',\n          message: 'Game results have been saved to history!'\n        }));\n      } catch (error) {\n        console.error('Failed to update history:', error);\n        dispatch(addToast({\n          type: 'error',\n          title: 'History Update Failed',\n          message: 'Failed to save game results. Please try again.'\n        }));\n      }\n    };\n    updateGameHistory();\n  }, [roomId, testName, isHistoryUpdated, dispatch]);\n\n  // Get winner information\n  const getWinnerInfo = () => {\n    if (!scores.length) return null;\n    const sortedScores = [...scores].sort((a, b) => parseInt(b.score.toString()) - parseInt(a.score.toString()));\n    const winner = sortedScores[0];\n    const isMultipleWinners = sortedScores.filter(s => s.score === winner.score).length > 1;\n    return {\n      winner,\n      isMultipleWinners,\n      topScores: sortedScores.slice(0, 3)\n    };\n  };\n  const winnerInfo = getWinnerInfo();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative w-screen h-screen overflow-hidden\",\n    children: [showConfetti && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 pointer-events-none z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-b from-transparent to-transparent\",\n        children: [...Array(50)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute animate-bounce\",\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`,\n            animationDelay: `${Math.random() * 2}s`,\n            animationDuration: `${2 + Math.random() * 2}s`\n          },\n          children: ['🎉', '🎊', '⭐', '🏆', '🎈'][Math.floor(Math.random() * 5)]\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 29\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-0 left-0 origin-top-left\",\n      style: {\n        transform: \"scale(0.75)\",\n        width: `${100 / 0.75}vw`,\n        height: `${100 / 0.75}vh`\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative min-h-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-b from-slate-900 via-blue-900 to-blue-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.3)_1px,transparent_1px),radial-gradient(circle_at_75%_75%,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[length:100px_100px]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-blue-500/50 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10 flex flex-col min-h-full\",\n          children: [isHost && /*#__PURE__*/_jsxDEV(Header, {\n            isHost: isHost\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 36\n          }, this), winnerInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-6xl mb-4\",\n              children: \"\\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-bold text-white mb-2\",\n              children: winnerInfo.isMultipleWinners ? 'We Have Winners!' : 'We Have a Winner!'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl text-yellow-300 font-semibold\",\n              children: winnerInfo.winner.playerName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl text-blue-200 mt-2\",\n              children: [\"Final Score: \", winnerInfo.winner.score, \" points\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-1 items-center justify-center w-full py-8\",\n            style: {\n              marginTop: isHost ? \"120px\" : \"60px\",\n              transform: \"scale(1.3)\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full max-w-2xl\",\n              children: [/*#__PURE__*/_jsxDEV(PlayerScore, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 bg-white/10 backdrop-blur-sm rounded-lg p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-semibold text-white mb-4 text-center\",\n                  children: \"Game Statistics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-4 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white/20 rounded-lg p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-2xl font-bold text-white\",\n                      children: players.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-200 text-sm\",\n                      children: \"Total Players\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white/20 rounded-lg p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-2xl font-bold text-white\",\n                      children: testName || 'Quiz Game'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-200 text-sm\",\n                      children: \"Game Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 37\n                }, this), winnerInfo && winnerInfo.topScores.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-semibold text-white mb-3 text-center\",\n                    children: \"Top 3 Players\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: winnerInfo.topScores.map((score, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center bg-white/20 rounded-lg p-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-2xl\",\n                          children: index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 207,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-white font-medium\",\n                          children: score.playerName\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 210,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-yellow-300 font-bold\",\n                        children: [score.score, \" pts\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 57\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 53\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 33\n              }, this), buttonComponent && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6 text-center\",\n                children: buttonComponent\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg text-blue-200 mb-2\",\n                  children: \"Thank you for playing!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-blue-300\",\n                  children: \"Game results have been saved to your history\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 9\n  }, this);\n};\n_s(FinalScore, \"3OgCRpA3nhqGbwwv9zHibQTaR/I=\", false, function () {\n  return [useSearchParams, useAppDispatch, useAppSelector, useFirebaseListener];\n});\n_c = FinalScore;\nexport default FinalScore;\nvar _c;\n$RefreshReg$(_c, \"FinalScore\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "useAppSelector", "useAppDispatch", "addToast", "useFirebaseListener", "PlayerScore", "Header", "jsxDEV", "_jsxDEV", "FinalScore", "isHost", "buttonComponent", "_s", "searchParams", "dispatch", "scores", "players", "loading", "state", "game", "roomId", "get", "testName", "isHistoryUpdated", "setIsHistoryUpdated", "showConfetti", "setShowConfetti", "listenToScores", "scoresData", "setTimeout", "updateGameHistory", "console", "log", "type", "title", "message", "error", "getWinnerInfo", "length", "sortedScores", "sort", "a", "b", "parseInt", "score", "toString", "winner", "isMultipleWinners", "filter", "s", "topScores", "slice", "winnerInfo", "className", "children", "Array", "map", "_", "i", "style", "left", "Math", "random", "top", "animationDelay", "animationDuration", "floor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transform", "width", "height", "<PERSON><PERSON><PERSON>", "marginTop", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/FinalScore.migrated.tsx"], "sourcesContent": ["// MIGRATED VERSION: FinalScore using Redux and new hooks\nimport React, { useState, useEffect, ReactNode, useRef, useCallback } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { useAppSelector, useAppDispatch } from '../app/store';\nimport { addToast } from '../app/store/slices/uiSlice';\nimport { useFirebaseListener } from '../shared/hooks';\nimport { GameState } from '../shared/types';\nimport PlayerScore from '../components/PlayerScore.migrated';\nimport Header from '../layouts/Header';\nimport { updateHistory } from './services';\n\ninterface PlayerScoreProps {\n    isHost?: boolean;\n    buttonComponent?: ReactNode;\n}\n\nconst FinalScore: React.FC<PlayerScoreProps> = ({ isHost = false, buttonComponent }) => {\n    const [searchParams] = useSearchParams();\n    const dispatch = useAppDispatch();\n    \n    // Redux state\n    const {\n        scores,\n        players,\n        loading\n    } = useAppSelector((state) => state.game as GameState);\n    \n    // URL params\n    const roomId = searchParams.get(\"roomId\") || \"\";\n    const testName = searchParams.get(\"testName\") || \"\";\n    \n    // Local state\n    const [isHistoryUpdated, setIsHistoryUpdated] = useState(false);\n    const [showConfetti, setShowConfetti] = useState(false);\n    \n    // Hooks\n    const { listenToScores } = useFirebaseListener(roomId);\n\n    // Listen to final scores\n    useEffect(() => {\n        if (!roomId) return;\n        \n        return listenToScores((scoresData) => {\n            if (scoresData) {\n                // Trigger confetti animation when scores are loaded\n                setShowConfetti(true);\n                setTimeout(() => setShowConfetti(false), 5000);\n            }\n        });\n    }, [roomId, listenToScores]);\n\n    // Update history when component mounts\n    useEffect(() => {\n        if (!roomId || !testName || isHistoryUpdated) return;\n        \n        const updateGameHistory = async () => {\n            try {\n                // Note: updateHistory function needs to be implemented\n                console.log('Updating game history:', { roomId, testName });\n                setIsHistoryUpdated(true);\n\n                dispatch(addToast({\n                    type: 'success',\n                    title: 'Game Complete',\n                    message: 'Game results have been saved to history!'\n                }));\n            } catch (error) {\n                console.error('Failed to update history:', error);\n                dispatch(addToast({\n                    type: 'error',\n                    title: 'History Update Failed',\n                    message: 'Failed to save game results. Please try again.'\n                }));\n            }\n        };\n\n        updateGameHistory();\n    }, [roomId, testName, isHistoryUpdated, dispatch]);\n\n    // Get winner information\n    const getWinnerInfo = () => {\n        if (!scores.length) return null;\n        \n        const sortedScores = [...scores].sort((a, b) =>\n            parseInt(b.score.toString()) - parseInt(a.score.toString())\n        );\n        \n        const winner = sortedScores[0];\n        const isMultipleWinners = sortedScores.filter(s => s.score === winner.score).length > 1;\n        \n        return {\n            winner,\n            isMultipleWinners,\n            topScores: sortedScores.slice(0, 3)\n        };\n    };\n\n    const winnerInfo = getWinnerInfo();\n\n    return (\n        <div className=\"relative w-screen h-screen overflow-hidden\">\n            {/* Confetti Animation */}\n            {showConfetti && (\n                <div className=\"fixed inset-0 pointer-events-none z-50\">\n                    <div className=\"absolute inset-0 bg-gradient-to-b from-transparent to-transparent\">\n                        {[...Array(50)].map((_, i) => (\n                            <div\n                                key={i}\n                                className=\"absolute animate-bounce\"\n                                style={{\n                                    left: `${Math.random() * 100}%`,\n                                    top: `${Math.random() * 100}%`,\n                                    animationDelay: `${Math.random() * 2}s`,\n                                    animationDuration: `${2 + Math.random() * 2}s`\n                                }}\n                            >\n                                {['🎉', '🎊', '⭐', '🏆', '🎈'][Math.floor(Math.random() * 5)]}\n                            </div>\n                        ))}\n                    </div>\n                </div>\n            )}\n\n            <div\n                className=\"absolute top-0 left-0 origin-top-left\"\n                style={{\n                    transform: \"scale(0.75)\",\n                    width: `${100 / 0.75}vw`,\n                    height: `${100 / 0.75}vh`,\n                }}\n            >\n                <div className=\"relative min-h-full\">\n                    {/* Ocean/Starry Night Background */}\n                    <div className=\"absolute inset-0 bg-gradient-to-b from-slate-900 via-blue-900 to-blue-600\">\n                        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.3)_1px,transparent_1px),radial-gradient(circle_at_75%_75%,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[length:100px_100px]\"></div>\n                        <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-blue-500/50 to-transparent\"></div>\n                        <div className=\"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse\"></div>\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"relative z-10 flex flex-col min-h-full\">\n                        {isHost && <Header isHost={isHost} />}\n                        \n                        {/* Winner Announcement */}\n                        {winnerInfo && (\n                            <div className=\"text-center py-8\">\n                                <div className=\"text-6xl mb-4\">🏆</div>\n                                <h1 className=\"text-4xl font-bold text-white mb-2\">\n                                    {winnerInfo.isMultipleWinners ? 'We Have Winners!' : 'We Have a Winner!'}\n                                </h1>\n                                <div className=\"text-2xl text-yellow-300 font-semibold\">\n                                    {winnerInfo.winner.playerName}\n                                </div>\n                                <div className=\"text-xl text-blue-200 mt-2\">\n                                    Final Score: {winnerInfo.winner.score} points\n                                </div>\n                            </div>\n                        )}\n\n                        <div className=\"flex flex-1 items-center justify-center w-full py-8\"\n                            style={{\n                                marginTop: isHost ? \"120px\" : \"60px\",\n                                transform: \"scale(1.3)\",\n                            }}\n                        >\n                            <div className=\"w-full max-w-2xl\">\n                                <PlayerScore />\n                                \n                                {/* Additional Game Stats */}\n                                <div className=\"mt-8 bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n                                    <h3 className=\"text-xl font-semibold text-white mb-4 text-center\">\n                                        Game Statistics\n                                    </h3>\n                                    \n                                    <div className=\"grid grid-cols-2 gap-4 text-center\">\n                                        <div className=\"bg-white/20 rounded-lg p-4\">\n                                            <div className=\"text-2xl font-bold text-white\">\n                                                {players.length}\n                                            </div>\n                                            <div className=\"text-blue-200 text-sm\">\n                                                Total Players\n                                            </div>\n                                        </div>\n                                        \n                                        <div className=\"bg-white/20 rounded-lg p-4\">\n                                            <div className=\"text-2xl font-bold text-white\">\n                                                {testName || 'Quiz Game'}\n                                            </div>\n                                            <div className=\"text-blue-200 text-sm\">\n                                                Game Name\n                                            </div>\n                                        </div>\n                                    </div>\n                                    \n                                    {winnerInfo && winnerInfo.topScores.length > 1 && (\n                                        <div className=\"mt-6\">\n                                            <h4 className=\"text-lg font-semibold text-white mb-3 text-center\">\n                                                Top 3 Players\n                                            </h4>\n                                            <div className=\"space-y-2\">\n                                                {winnerInfo.topScores.map((score, index) => (\n                                                    <div \n                                                        key={index}\n                                                        className=\"flex justify-between items-center bg-white/20 rounded-lg p-3\"\n                                                    >\n                                                        <div className=\"flex items-center gap-3\">\n                                                            <div className=\"text-2xl\">\n                                                                {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}\n                                                            </div>\n                                                            <span className=\"text-white font-medium\">\n                                                                {score.playerName}\n                                                            </span>\n                                                        </div>\n                                                        <span className=\"text-yellow-300 font-bold\">\n                                                            {score.score} pts\n                                                        </span>\n                                                    </div>\n                                                ))}\n                                            </div>\n                                        </div>\n                                    )}\n                                </div>\n\n                                {/* Custom Button Component */}\n                                {buttonComponent && (\n                                    <div className=\"mt-6 text-center\">\n                                        {buttonComponent}\n                                    </div>\n                                )}\n                                \n                                {/* Thank You Message */}\n                                <div className=\"mt-8 text-center\">\n                                    <div className=\"text-lg text-blue-200 mb-2\">\n                                        Thank you for playing!\n                                    </div>\n                                    <div className=\"text-sm text-blue-300\">\n                                        Game results have been saved to your history\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default FinalScore;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAwC,OAAO;AAClF,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AAC7D,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,mBAAmB,QAAQ,iBAAiB;AAErD,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQvC,MAAMC,UAAsC,GAAGA,CAAC;EAAEC,MAAM,GAAG,KAAK;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,YAAY,CAAC,GAAGb,eAAe,CAAC,CAAC;EACxC,MAAMc,QAAQ,GAAGZ,cAAc,CAAC,CAAC;;EAEjC;EACA,MAAM;IACFa,MAAM;IACNC,OAAO;IACPC;EACJ,CAAC,GAAGhB,cAAc,CAAEiB,KAAK,IAAKA,KAAK,CAACC,IAAiB,CAAC;;EAEtD;EACA,MAAMC,MAAM,GAAGP,YAAY,CAACQ,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC/C,MAAMC,QAAQ,GAAGT,YAAY,CAACQ,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;;EAEnD;EACA,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM;IAAE6B;EAAe,CAAC,GAAGvB,mBAAmB,CAACgB,MAAM,CAAC;;EAEtD;EACArB,SAAS,CAAC,MAAM;IACZ,IAAI,CAACqB,MAAM,EAAE;IAEb,OAAOO,cAAc,CAAEC,UAAU,IAAK;MAClC,IAAIA,UAAU,EAAE;QACZ;QACAF,eAAe,CAAC,IAAI,CAAC;QACrBG,UAAU,CAAC,MAAMH,eAAe,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAClD;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,CAACN,MAAM,EAAEO,cAAc,CAAC,CAAC;;EAE5B;EACA5B,SAAS,CAAC,MAAM;IACZ,IAAI,CAACqB,MAAM,IAAI,CAACE,QAAQ,IAAIC,gBAAgB,EAAE;IAE9C,MAAMO,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACA;QACAC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;UAAEZ,MAAM;UAAEE;QAAS,CAAC,CAAC;QAC3DE,mBAAmB,CAAC,IAAI,CAAC;QAEzBV,QAAQ,CAACX,QAAQ,CAAC;UACd8B,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,eAAe;UACtBC,OAAO,EAAE;QACb,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZL,OAAO,CAACK,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDtB,QAAQ,CAACX,QAAQ,CAAC;UACd8B,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,uBAAuB;UAC9BC,OAAO,EAAE;QACb,CAAC,CAAC,CAAC;MACP;IACJ,CAAC;IAEDL,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACV,MAAM,EAAEE,QAAQ,EAAEC,gBAAgB,EAAET,QAAQ,CAAC,CAAC;;EAElD;EACA,MAAMuB,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACtB,MAAM,CAACuB,MAAM,EAAE,OAAO,IAAI;IAE/B,MAAMC,YAAY,GAAG,CAAC,GAAGxB,MAAM,CAAC,CAACyB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACvCC,QAAQ,CAACD,CAAC,CAACE,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAGF,QAAQ,CAACF,CAAC,CAACG,KAAK,CAACC,QAAQ,CAAC,CAAC,CAC9D,CAAC;IAED,MAAMC,MAAM,GAAGP,YAAY,CAAC,CAAC,CAAC;IAC9B,MAAMQ,iBAAiB,GAAGR,YAAY,CAACS,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACL,KAAK,KAAKE,MAAM,CAACF,KAAK,CAAC,CAACN,MAAM,GAAG,CAAC;IAEvF,OAAO;MACHQ,MAAM;MACNC,iBAAiB;MACjBG,SAAS,EAAEX,YAAY,CAACY,KAAK,CAAC,CAAC,EAAE,CAAC;IACtC,CAAC;EACL,CAAC;EAED,MAAMC,UAAU,GAAGf,aAAa,CAAC,CAAC;EAElC,oBACI7B,OAAA;IAAK6C,SAAS,EAAC,4CAA4C;IAAAC,QAAA,GAEtD7B,YAAY,iBACTjB,OAAA;MAAK6C,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACnD9C,OAAA;QAAK6C,SAAS,EAAC,mEAAmE;QAAAC,QAAA,EAC7E,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACrBlD,OAAA;UAEI6C,SAAS,EAAC,yBAAyB;UACnCM,KAAK,EAAE;YACHC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;YAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;YAC9BE,cAAc,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;YACvCG,iBAAiB,EAAE,GAAG,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/C,CAAE;UAAAR,QAAA,EAED,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAACO,IAAI,CAACK,KAAK,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QAAC,GATxDJ,CAAC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUL,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAED9D,OAAA;MACI6C,SAAS,EAAC,uCAAuC;MACjDM,KAAK,EAAE;QACHY,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,GAAG,GAAG,GAAG,IAAI,IAAI;QACxBC,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI;MACzB,CAAE;MAAAnB,QAAA,eAEF9C,OAAA;QAAK6C,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAEhC9C,OAAA;UAAK6C,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACtF9C,OAAA;YAAK6C,SAAS,EAAC;UAAyM;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/N9D,OAAA;YAAK6C,SAAS,EAAC;UAAwF;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9G9D,OAAA;YAAK6C,SAAS,EAAC;UAAsH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC,eAGN9D,OAAA;UAAK6C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GAClD5C,MAAM,iBAAIF,OAAA,CAACF,MAAM;YAACI,MAAM,EAAEA;UAAO;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAGpClB,UAAU,iBACP5C,OAAA;YAAK6C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC7B9C,OAAA;cAAK6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvC9D,OAAA;cAAI6C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAC7CF,UAAU,CAACL,iBAAiB,GAAG,kBAAkB,GAAG;YAAmB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACL9D,OAAA;cAAK6C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAClDF,UAAU,CAACN,MAAM,CAAC4B;YAAU;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACN9D,OAAA;cAAK6C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,eAC3B,EAACF,UAAU,CAACN,MAAM,CAACF,KAAK,EAAC,SAC1C;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAED9D,OAAA;YAAK6C,SAAS,EAAC,qDAAqD;YAChEM,KAAK,EAAE;cACHgB,SAAS,EAAEjE,MAAM,GAAG,OAAO,GAAG,MAAM;cACpC6D,SAAS,EAAE;YACf,CAAE;YAAAjB,QAAA,eAEF9C,OAAA;cAAK6C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC7B9C,OAAA,CAACH,WAAW;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGf9D,OAAA;gBAAK6C,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAC7D9C,OAAA;kBAAI6C,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAAC;gBAElE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEL9D,OAAA;kBAAK6C,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,gBAC/C9C,OAAA;oBAAK6C,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACvC9C,OAAA;sBAAK6C,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,EACzCtC,OAAO,CAACsB;oBAAM;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC,eACN9D,OAAA;sBAAK6C,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAEvC;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAEN9D,OAAA;oBAAK6C,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACvC9C,OAAA;sBAAK6C,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,EACzChC,QAAQ,IAAI;oBAAW;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,eACN9D,OAAA;sBAAK6C,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAEvC;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EAELlB,UAAU,IAAIA,UAAU,CAACF,SAAS,CAACZ,MAAM,GAAG,CAAC,iBAC1C9B,OAAA;kBAAK6C,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACjB9C,OAAA;oBAAI6C,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,EAAC;kBAElE;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL9D,OAAA;oBAAK6C,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACrBF,UAAU,CAACF,SAAS,CAACM,GAAG,CAAC,CAACZ,KAAK,EAAEgC,KAAK,kBACnCpE,OAAA;sBAEI6C,SAAS,EAAC,8DAA8D;sBAAAC,QAAA,gBAExE9C,OAAA;wBAAK6C,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACpC9C,OAAA;0BAAK6C,SAAS,EAAC,UAAU;0BAAAC,QAAA,EACpBsB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG;wBAAI;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C,CAAC,eACN9D,OAAA;0BAAM6C,SAAS,EAAC,wBAAwB;0BAAAC,QAAA,EACnCV,KAAK,CAAC8B;wBAAU;0BAAAP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACN9D,OAAA;wBAAM6C,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,GACtCV,KAAK,CAACA,KAAK,EAAC,MACjB;sBAAA;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,GAbFM,KAAK;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAcT,CACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,EAGL3D,eAAe,iBACZH,OAAA;gBAAK6C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC5B3C;cAAe;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACR,eAGD9D,OAAA;gBAAK6C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC7B9C,OAAA;kBAAK6C,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE5C;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9D,OAAA;kBAAK6C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAEvC;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC1D,EAAA,CAtOIH,UAAsC;EAAA,QACjBT,eAAe,EACrBE,cAAc,EAO3BD,cAAc,EAWSG,mBAAmB;AAAA;AAAAyE,EAAA,GApB5CpE,UAAsC;AAwO5C,eAAeA,UAAU;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}