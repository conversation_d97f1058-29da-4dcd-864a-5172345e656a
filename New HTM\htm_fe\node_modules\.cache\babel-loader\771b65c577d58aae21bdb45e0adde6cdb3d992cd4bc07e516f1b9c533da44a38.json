{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\pages\\\\FinalScore\\\\PlayerFinalScore.migrated.tsx\",\n  _s = $RefreshSig$();\n// MIGRATED VERSION: PlayerFinalScore using Redux and new hooks\nimport React, { useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { useAppDispatch } from '../../app/store';\nimport { addToast } from '../../app/store/slices/uiSlice';\nimport { useFirebaseListener } from '../../shared/hooks';\nimport FinalScore from '../../components/FinalScore.migrated';\nimport { deletePath } from '../../services/firebaseServices';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PlayerFinalScore = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const dispatch = useAppDispatch();\n  const roomId = searchParams.get(\"roomId\") || \"\";\n\n  // Firebase listeners\n  const {\n    listenToSound\n  } = useFirebaseListener(roomId);\n  useEffect(() => {\n    if (!roomId) return;\n    const unsubscribeSound = listenToSound(async type => {\n      try {\n        // Create and play audio element\n        const audio = new Audio();\n\n        // Map sound types to audio files\n        const soundMap = {\n          'correct': '/sounds/correct.mp3',\n          'incorrect': '/sounds/incorrect.mp3',\n          'next': '/sounds/next.mp3',\n          'finish': '/sounds/finish.mp3',\n          'applause': '/sounds/applause.mp3',\n          'countdown': '/sounds/countdown.mp3'\n        };\n        const soundFile = soundMap[type];\n        if (soundFile) {\n          audio.src = soundFile;\n          audio.volume = 0.7; // Set volume to 70%\n\n          try {\n            await audio.play();\n            console.log(`Playing sound: ${type}`);\n          } catch (playError) {\n            console.warn(`Could not play sound ${type}:`, playError);\n            // Fallback: show toast notification instead\n            dispatch(addToast({\n              type: 'info',\n              title: 'Sound Effect',\n              message: `Sound: ${type}`,\n              duration: 2000\n            }));\n          }\n        } else {\n          console.warn(`Unknown sound type: ${type}`);\n        }\n\n        // Clean up the sound path in Firebase\n        await deletePath(roomId, \"sound\");\n      } catch (error) {\n        console.error(\"Error handling sound:\", error);\n        dispatch(addToast({\n          type: 'error',\n          title: 'Sound Error',\n          message: 'Failed to play sound effect'\n        }));\n      }\n    });\n    return () => {\n      if (unsubscribeSound) {\n        unsubscribeSound();\n      }\n    };\n  }, [roomId, listenToSound, dispatch]);\n  return /*#__PURE__*/_jsxDEV(FinalScore, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 9\n  }, this);\n};\n_s(PlayerFinalScore, \"DPe6ttcqSwat95+mHBtS0S2XWDg=\", false, function () {\n  return [useSearchParams, useAppDispatch, useFirebaseListener];\n});\n_c = PlayerFinalScore;\nexport default PlayerFinalScore;\nvar _c;\n$RefreshReg$(_c, \"PlayerFinalScore\");", "map": {"version": 3, "names": ["React", "useEffect", "useSearchParams", "useAppDispatch", "addToast", "useFirebaseListener", "FinalScore", "deletePath", "jsxDEV", "_jsxDEV", "PlayerFinalScore", "_s", "searchParams", "dispatch", "roomId", "get", "listenToSound", "unsubscribeSound", "type", "audio", "Audio", "soundMap", "soundFile", "src", "volume", "play", "console", "log", "playError", "warn", "title", "message", "duration", "error", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/pages/FinalScore/PlayerFinalScore.migrated.tsx"], "sourcesContent": ["// MIGRATED VERSION: PlayerFinalScore using Redux and new hooks\nimport React, { useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { useAppDispatch } from '../../app/store';\nimport { addToast } from '../../app/store/slices/uiSlice';\nimport { useFirebaseListener } from '../../shared/hooks';\nimport FinalScore from '../../components/FinalScore.migrated';\nimport { deletePath } from '../../services/firebaseServices';\n\nconst PlayerFinalScore: React.FC = () => {\n    const [searchParams] = useSearchParams();\n    const dispatch = useAppDispatch();\n    const roomId = searchParams.get(\"roomId\") || \"\";\n    \n    // Firebase listeners\n    const { listenToSound } = useFirebaseListener(roomId);\n\n    useEffect(() => {\n        if (!roomId) return;\n        \n        const unsubscribeSound = listenToSound(async (type) => {\n            try {\n                // Create and play audio element\n                const audio = new Audio();\n                \n                // Map sound types to audio files\n                const soundMap: { [key: string]: string } = {\n                    'correct': '/sounds/correct.mp3',\n                    'incorrect': '/sounds/incorrect.mp3',\n                    'next': '/sounds/next.mp3',\n                    'finish': '/sounds/finish.mp3',\n                    'applause': '/sounds/applause.mp3',\n                    'countdown': '/sounds/countdown.mp3'\n                };\n\n                const soundFile = soundMap[type];\n                if (soundFile) {\n                    audio.src = soundFile;\n                    audio.volume = 0.7; // Set volume to 70%\n                    \n                    try {\n                        await audio.play();\n                        console.log(`Playing sound: ${type}`);\n                    } catch (playError) {\n                        console.warn(`Could not play sound ${type}:`, playError);\n                        // Fallback: show toast notification instead\n                        dispatch(addToast({\n                            type: 'info',\n                            title: 'Sound Effect',\n                            message: `Sound: ${type}`,\n                            duration: 2000\n                        }));\n                    }\n                } else {\n                    console.warn(`Unknown sound type: ${type}`);\n                }\n\n                // Clean up the sound path in Firebase\n                await deletePath(roomId, \"sound\");\n                \n            } catch (error) {\n                console.error(\"Error handling sound:\", error);\n                dispatch(addToast({\n                    type: 'error',\n                    title: 'Sound Error',\n                    message: 'Failed to play sound effect'\n                }));\n            }\n        });\n\n        return () => {\n            if (unsubscribeSound) {\n                unsubscribeSound();\n            }\n        };\n    }, [roomId, listenToSound, dispatch]);\n\n    return (\n        <FinalScore />\n    );\n};\n\nexport default PlayerFinalScore;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,SAASC,UAAU,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,YAAY,CAAC,GAAGV,eAAe,CAAC,CAAC;EACxC,MAAMW,QAAQ,GAAGV,cAAc,CAAC,CAAC;EACjC,MAAMW,MAAM,GAAGF,YAAY,CAACG,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;;EAE/C;EACA,MAAM;IAAEC;EAAc,CAAC,GAAGX,mBAAmB,CAACS,MAAM,CAAC;EAErDb,SAAS,CAAC,MAAM;IACZ,IAAI,CAACa,MAAM,EAAE;IAEb,MAAMG,gBAAgB,GAAGD,aAAa,CAAC,MAAOE,IAAI,IAAK;MACnD,IAAI;QACA;QACA,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;;QAEzB;QACA,MAAMC,QAAmC,GAAG;UACxC,SAAS,EAAE,qBAAqB;UAChC,WAAW,EAAE,uBAAuB;UACpC,MAAM,EAAE,kBAAkB;UAC1B,QAAQ,EAAE,oBAAoB;UAC9B,UAAU,EAAE,sBAAsB;UAClC,WAAW,EAAE;QACjB,CAAC;QAED,MAAMC,SAAS,GAAGD,QAAQ,CAACH,IAAI,CAAC;QAChC,IAAII,SAAS,EAAE;UACXH,KAAK,CAACI,GAAG,GAAGD,SAAS;UACrBH,KAAK,CAACK,MAAM,GAAG,GAAG,CAAC,CAAC;;UAEpB,IAAI;YACA,MAAML,KAAK,CAACM,IAAI,CAAC,CAAC;YAClBC,OAAO,CAACC,GAAG,CAAC,kBAAkBT,IAAI,EAAE,CAAC;UACzC,CAAC,CAAC,OAAOU,SAAS,EAAE;YAChBF,OAAO,CAACG,IAAI,CAAC,wBAAwBX,IAAI,GAAG,EAAEU,SAAS,CAAC;YACxD;YACAf,QAAQ,CAACT,QAAQ,CAAC;cACdc,IAAI,EAAE,MAAM;cACZY,KAAK,EAAE,cAAc;cACrBC,OAAO,EAAE,UAAUb,IAAI,EAAE;cACzBc,QAAQ,EAAE;YACd,CAAC,CAAC,CAAC;UACP;QACJ,CAAC,MAAM;UACHN,OAAO,CAACG,IAAI,CAAC,uBAAuBX,IAAI,EAAE,CAAC;QAC/C;;QAEA;QACA,MAAMX,UAAU,CAACO,MAAM,EAAE,OAAO,CAAC;MAErC,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACZP,OAAO,CAACO,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CpB,QAAQ,CAACT,QAAQ,CAAC;UACdc,IAAI,EAAE,OAAO;UACbY,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE;QACb,CAAC,CAAC,CAAC;MACP;IACJ,CAAC,CAAC;IAEF,OAAO,MAAM;MACT,IAAId,gBAAgB,EAAE;QAClBA,gBAAgB,CAAC,CAAC;MACtB;IACJ,CAAC;EACL,CAAC,EAAE,CAACH,MAAM,EAAEE,aAAa,EAAEH,QAAQ,CAAC,CAAC;EAErC,oBACIJ,OAAA,CAACH,UAAU;IAAA4B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAEtB,CAAC;AAAC1B,EAAA,CAvEID,gBAA0B;EAAA,QACLR,eAAe,EACrBC,cAAc,EAILE,mBAAmB;AAAA;AAAAiC,EAAA,GAN3C5B,gBAA0B;AAyEhC,eAAeA,gBAAgB;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}