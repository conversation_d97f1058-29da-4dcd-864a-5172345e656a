{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\pages\\\\User\\\\InformationForm\\\\InformationForm.migrated.tsx\",\n  _s = $RefreshSig$();\n// MIGRATED VERSION: InformationForm using Redux and new hooks\nimport React, { useState } from \"react\";\nimport { useSearchParams, useNavigate } from \"react-router-dom\";\nimport { useAppDispatch } from \"../../../app/store\";\nimport { setPlayers } from \"../../../app/store/slices/gameSlice\";\nimport { setCurrentRoom } from \"../../../app/store/slices/roomSlice\";\nimport { addToast } from \"../../../app/store/slices/uiSlice\";\nimport { uploadFile } from \"../../../services/uploadAssestServices\";\nimport roomService from \"../../../services/room.service\";\nimport useTokenRefresh from \"../../../hooks/useTokenRefresh\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InformationForm = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const roomId = searchParams.get(\"roomid\");\n  const [username, setUsername] = useState(\"\");\n  const [playerNumber, setPlayerNumber] = useState(\"\");\n  const [avatar, setAvatar] = useState(null);\n  const [uploading, setUploading] = useState(false);\n  const [password, setPassword] = useState(searchParams.get(\"password\") || \"\");\n  const defaultAvatar = \"https://via.placeholder.com/100\";\n\n  // Initialize token refresh for player\n  useTokenRefresh();\n\n  // Handle avatar upload to S3\n  const handleAvatarUpload = async event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (!file) return;\n    try {\n      setUploading(true);\n      const uploadedKey = await uploadFile(file, \"User avatar\");\n      console.log(`https://d1fc7d6en42vzg.cloudfront.net/${uploadedKey}`);\n      setAvatar(`https://d1fc7d6en42vzg.cloudfront.net/${uploadedKey}`);\n      setUploading(false);\n      dispatch(addToast({\n        type: 'success',\n        title: 'Upload thành công',\n        message: 'Ảnh đại diện đã được tải lên!'\n      }));\n    } catch (error) {\n      setUploading(false);\n      console.error(error);\n      dispatch(addToast({\n        type: 'error',\n        title: 'Upload thất bại',\n        message: 'Upload ảnh đại diện thất bại. Vui lòng thử lại.'\n      }));\n    }\n  };\n  const handleSubmit = async () => {\n    if (username && playerNumber && roomId && avatar) {\n      try {\n        const result = await roomService.joinRoom(roomId, {\n          userName: username,\n          stt: playerNumber,\n          avatar: avatar\n        }, password || undefined);\n        console.log(\"result\", result);\n        const currentPlayer = result.players.find(player => player.stt === playerNumber);\n        console.log(\"currentPlayer\", currentPlayer);\n\n        // Store in localStorage for backward compatibility\n        localStorage.setItem(\"currentPlayer\", JSON.stringify(currentPlayer));\n        localStorage.setItem(\"currentAvatar\", JSON.stringify(avatar));\n        localStorage.setItem(\"roomId\", roomId);\n        localStorage.setItem(\"position\", playerNumber);\n        localStorage.setItem(\"currentPlayerName\", username);\n\n        // Update Redux state instead of context\n        dispatch(setPlayers(result.players));\n        dispatch(setCurrentRoom({\n          id: roomId,\n          name: result.roomName || \"Game Room\",\n          players: result.players,\n          isActive: true\n        }));\n        dispatch(addToast({\n          type: 'success',\n          title: 'Tham gia thành công',\n          message: `Chào mừng ${username} đến với phòng thi!`\n        }));\n        navigate(`/play?round=1&roomId=${roomId}`);\n      } catch (error) {\n        console.error(error);\n        dispatch(addToast({\n          type: 'error',\n          title: 'Tham gia thất bại',\n          message: 'Không thể tham gia phòng. Vui lòng thử lại.'\n        }));\n      }\n    } else {\n      dispatch(addToast({\n        type: 'warning',\n        title: 'Thông tin chưa đầy đủ',\n        message: 'Vui lòng nhập tên người dùng, chọn số thứ tự và tải ảnh đại diện!'\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-gradient-to-b from-slate-900 via-blue-900 to-blue-600\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.3)_1px,transparent_1px),radial-gradient(circle_at_75%_75%,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[length:100px_100px]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-blue-500/50 to-transparent\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 flex items-center justify-center min-h-screen p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"font-serif text-4xl font-bold mb-4 text-transparent bg-gradient-to-r from-blue-200 to-cyan-100 bg-clip-text\",\n            children: \"H\\xE0nh Tr\\xECnh Magellan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-200/90 text-lg\",\n            children: \"Nh\\u1EADp th\\xF4ng tin \\u0111\\u1EC3 tham gia ph\\xF2ng thi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-slate-800/80 backdrop-blur-sm border border-blue-400/30 rounded-xl shadow-2xl p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-white text-center mb-6\",\n            children: \"Th\\xF4ng tin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: e => e.preventDefault(),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-blue-200 text-sm font-medium mb-2\",\n                htmlFor: \"email\",\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \"w-full px-4 py-3 bg-slate-700/50 border border-blue-400/30 rounded-lg text-white placeholder-blue-300/50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent backdrop-blur-sm\",\n                  id: \"email\",\n                  placeholder: \"Nh\\u1EADp t\\xEAn c\\u1EE7a b\\u1EA1n \",\n                  value: username,\n                  onChange: e => setUsername(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300/50\",\n                  children: \"\\uD83D\\uDCE7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-blue-200 text-sm font-medium mb-2\",\n                htmlFor: \"password\",\n                children: \"S\\u1ED1 th\\u1EE9 t\\u1EF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \"w-full px-4 py-3 bg-slate-700/50 border border-blue-400/30 rounded-lg text-white placeholder-blue-300/50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent backdrop-blur-sm\",\n                  id: \"password\",\n                  placeholder: \"Nh\\u1EADp s\\u1ED1 th\\u1EE9 t\\u1EF1\",\n                  value: playerNumber,\n                  onChange: e => setPlayerNumber(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300/50\",\n                  children: \"\\uD83D\\uDD12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-blue-200 text-sm font-medium mb-2\",\n                htmlFor: \"roomPassword\",\n                children: \"M\\u1EADt kh\\u1EA9u ph\\xF2ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \"w-full px-4 py-3 bg-slate-700/50 border border-blue-400/30 rounded-lg text-white placeholder-blue-300/50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent backdrop-blur-sm\",\n                  type: \"password\",\n                  id: \"roomPassword\",\n                  placeholder: \"Nh\\u1EADp m\\u1EADt kh\\u1EA9u ph\\xF2ng (n\\u1EBFu c\\xF3)\",\n                  value: password,\n                  onChange: e => setPassword(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300/50\",\n                  children: \"\\uD83D\\uDD10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-blue-300/60 text-xs mt-1\",\n                children: \"\\u0110\\u1EC3 tr\\u1ED1ng n\\u1EBFu ph\\xF2ng kh\\xF4ng c\\xF3 m\\u1EADt kh\\u1EA9u\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: avatar ? `${avatar}` : defaultAvatar,\n                alt: \"Avatar\",\n                className: \"w-24 h-24 rounded-full mx-auto mb-2 object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Ch\\u1ECDn \\u1EA3nh \\u0111\\u1EA1i di\\u1EC7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  className: \"hidden\",\n                  accept: \"image/*\",\n                  onChange: handleAvatarUpload,\n                  disabled: uploading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-4 py-2 text-white rounded cursor-pointer ${uploading ? \"bg-gray-400 cursor-not-allowed\" : \"bg-blue-500 hover:bg-blue-600\"}`,\n                  children: uploading ? \"Đang tải...\" : \"Tải ảnh đại diện\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmit,\n              className: \"w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600\",\n              disabled: uploading,\n              children: \"X\\xE1c nh\\u1EADn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(InformationForm, \"PDlXZ/+1YvG98RHXcvQm9sP4Ess=\", false, function () {\n  return [useSearchParams, useNavigate, useAppDispatch, useTokenRefresh];\n});\n_c = InformationForm;\nexport default InformationForm;\nvar _c;\n$RefreshReg$(_c, \"InformationForm\");", "map": {"version": 3, "names": ["React", "useState", "useSearchParams", "useNavigate", "useAppDispatch", "setPlayers", "setCurrentRoom", "addToast", "uploadFile", "roomService", "useTokenRefresh", "jsxDEV", "_jsxDEV", "InformationForm", "_s", "searchParams", "navigate", "dispatch", "roomId", "get", "username", "setUsername", "<PERSON><PERSON><PERSON><PERSON>", "setPlayerNumber", "avatar", "set<PERSON>vat<PERSON>", "uploading", "setUploading", "password", "setPassword", "defaultAvatar", "handleAvatarUpload", "event", "_event$target$files", "file", "target", "files", "<PERSON><PERSON><PERSON>", "console", "log", "type", "title", "message", "error", "handleSubmit", "result", "joinRoom", "userName", "stt", "undefined", "currentPlayer", "players", "find", "player", "localStorage", "setItem", "JSON", "stringify", "id", "name", "roomName", "isActive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "e", "preventDefault", "htmlFor", "placeholder", "value", "onChange", "src", "alt", "accept", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/pages/User/InformationForm/InformationForm.migrated.tsx"], "sourcesContent": ["// MIGRATED VERSION: InformationForm using Redux and new hooks\nimport React, { useState } from \"react\";\nimport { useSearchParams, useNavigate } from \"react-router-dom\";\nimport { useAppDispatch } from \"../../../app/store\";\nimport { setPlayers } from \"../../../app/store/slices/gameSlice\";\nimport { setCurrentRoom } from \"../../../app/store/slices/roomSlice\";\nimport { addToast } from \"../../../app/store/slices/uiSlice\";\nimport { uploadFile } from \"../../../services/uploadAssestServices\";\nimport roomService from \"../../../services/room.service\";\nimport useTokenRefresh from \"../../../hooks/useTokenRefresh\";\n\nconst InformationForm = () => {\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  \n  const roomId = searchParams.get(\"roomid\");\n  const [username, setUsername] = useState(\"\");\n  const [playerNumber, setPlayerNumber] = useState(\"\");\n  const [avatar, setAvatar] = useState<string | null>(null);\n  const [uploading, setUploading] = useState(false);\n  const [password, setPassword] = useState(searchParams.get(\"password\") || \"\");\n\n  const defaultAvatar = \"https://via.placeholder.com/100\";\n\n  // Initialize token refresh for player\n  useTokenRefresh();\n\n  // Handle avatar upload to S3\n  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    try {\n      setUploading(true);\n      const uploadedKey = await uploadFile(file, \"User avatar\");\n      console.log(`https://d1fc7d6en42vzg.cloudfront.net/${uploadedKey}`);\n\n      setAvatar(`https://d1fc7d6en42vzg.cloudfront.net/${uploadedKey}`);\n      setUploading(false);\n      \n      dispatch(addToast({\n        type: 'success',\n        title: 'Upload thành công',\n        message: 'Ảnh đại diện đã được tải lên!'\n      }));\n    } catch (error) {\n      setUploading(false);\n      console.error(error);\n      \n      dispatch(addToast({\n        type: 'error',\n        title: 'Upload thất bại',\n        message: 'Upload ảnh đại diện thất bại. Vui lòng thử lại.'\n      }));\n    }\n  };\n\n  const handleSubmit = async () => {\n    if (username && playerNumber && roomId && avatar) {\n      try {\n        const result = await roomService.joinRoom(roomId, {\n          userName: username,\n          stt: playerNumber,\n          avatar: avatar,\n        }, password || undefined);\n\n        console.log(\"result\", result);\n\n        const currentPlayer = result.players.find((player: any) => player.stt === playerNumber);\n        console.log(\"currentPlayer\", currentPlayer);\n\n        // Store in localStorage for backward compatibility\n        localStorage.setItem(\"currentPlayer\", JSON.stringify(currentPlayer));\n        localStorage.setItem(\"currentAvatar\", JSON.stringify(avatar));\n        localStorage.setItem(\"roomId\", roomId);\n        localStorage.setItem(\"position\", playerNumber);\n        localStorage.setItem(\"currentPlayerName\", username);\n\n        // Update Redux state instead of context\n        dispatch(setPlayers(result.players));\n        dispatch(setCurrentRoom({\n          id: roomId,\n          name: result.roomName || \"Game Room\",\n          players: result.players,\n          isActive: true\n        }));\n\n        dispatch(addToast({\n          type: 'success',\n          title: 'Tham gia thành công',\n          message: `Chào mừng ${username} đến với phòng thi!`\n        }));\n\n        navigate(`/play?round=1&roomId=${roomId}`);\n      } catch (error) {\n        console.error(error);\n        \n        dispatch(addToast({\n          type: 'error',\n          title: 'Tham gia thất bại',\n          message: 'Không thể tham gia phòng. Vui lòng thử lại.'\n        }));\n      }\n    } else {\n      dispatch(addToast({\n        type: 'warning',\n        title: 'Thông tin chưa đầy đủ',\n        message: 'Vui lòng nhập tên người dùng, chọn số thứ tự và tải ảnh đại diện!'\n      }));\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      {/* Ocean/Starry Night Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-slate-900 via-blue-900 to-blue-600\">\n        {/* Stars overlay */}\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.3)_1px,transparent_1px),radial-gradient(circle_at_75%_75%,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[length:100px_100px]\"></div>\n        {/* Ocean waves effect */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-blue-500/50 to-transparent\"></div>\n        {/* Animated waves */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse\"></div>\n      </div>\n\n      {/* Content overlay */}\n      <div className=\"relative z-10 flex items-center justify-center min-h-screen p-4\">\n        <div className=\"w-full max-w-md\">\n          {/* Welcome Section */}\n          <div className=\"text-center mb-8\">\n            <h1 className=\"font-serif text-4xl font-bold mb-4 text-transparent bg-gradient-to-r from-blue-200 to-cyan-100 bg-clip-text\">\n              Hành Trình Magellan\n            </h1>\n            <p className=\"text-blue-200/90 text-lg\">\n              Nhập thông tin để tham gia phòng thi\n            </p>\n          </div>\n\n          {/* Login Form */}\n          <div className=\"bg-slate-800/80 backdrop-blur-sm border border-blue-400/30 rounded-xl shadow-2xl p-8\">\n            <h2 className=\"text-2xl font-bold text-white text-center mb-6\">\n              Thông tin\n            </h2>\n\n            <form onSubmit={(e) => e.preventDefault()}>\n              <div className=\"mb-6\">\n                <label className=\"block text-blue-200 text-sm font-medium mb-2\" htmlFor=\"email\">\n                  Name\n                </label>\n                <div className=\"relative\">\n                  <input\n                    className=\"w-full px-4 py-3 bg-slate-700/50 border border-blue-400/30 rounded-lg text-white placeholder-blue-300/50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent backdrop-blur-sm\"\n                    id=\"email\"\n                    placeholder=\"Nhập tên của bạn \"\n                    value={username}\n                    onChange={(e) => setUsername(e.target.value)}\n                  />\n                  <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300/50\">\n                    📧\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"mb-6\">\n                <label className=\"block text-blue-200 text-sm font-medium mb-2\" htmlFor=\"password\">\n                  Số thứ tự\n                </label>\n                <div className=\"relative\">\n                  <input\n                    className=\"w-full px-4 py-3 bg-slate-700/50 border border-blue-400/30 rounded-lg text-white placeholder-blue-300/50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent backdrop-blur-sm\"\n                    id=\"password\"\n                    placeholder=\"Nhập số thứ tự\"\n                    value={playerNumber}\n                    onChange={(e) => setPlayerNumber(e.target.value)}\n                  />\n                  <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300/50\">\n                    🔒\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"mb-6\">\n                <label className=\"block text-blue-200 text-sm font-medium mb-2\" htmlFor=\"roomPassword\">\n                  Mật khẩu phòng\n                </label>\n                <div className=\"relative\">\n                  <input\n                    className=\"w-full px-4 py-3 bg-slate-700/50 border border-blue-400/30 rounded-lg text-white placeholder-blue-300/50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent backdrop-blur-sm\"\n                    type=\"password\"\n                    id=\"roomPassword\"\n                    placeholder=\"Nhập mật khẩu phòng (nếu có)\"\n                    value={password}\n                    onChange={(e) => setPassword(e.target.value)}\n                  />\n                  <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300/50\">\n                    🔐\n                  </span>\n                </div>\n                <p className=\"text-blue-300/60 text-xs mt-1\">\n                  Để trống nếu phòng không có mật khẩu\n                </p>\n              </div>\n\n              <div className=\"mb-4 text-center\">\n                <img\n                  src={avatar ? `${avatar}` : defaultAvatar}\n                  alt=\"Avatar\"\n                  className=\"w-24 h-24 rounded-full mx-auto mb-2 object-cover\"\n                />\n                <label className=\"block cursor-pointer\">\n                  <span className=\"sr-only\">Chọn ảnh đại diện</span>\n                  <input\n                    type=\"file\"\n                    className=\"hidden\"\n                    accept=\"image/*\"\n                    onChange={handleAvatarUpload}\n                    disabled={uploading}\n                  />\n                  <span\n                    className={`px-4 py-2 text-white rounded cursor-pointer ${uploading ? \"bg-gray-400 cursor-not-allowed\" : \"bg-blue-500 hover:bg-blue-600\"\n                      }`}\n                  >\n                    {uploading ? \"Đang tải...\" : \"Tải ảnh đại diện\"}\n                  </span>\n                </label>\n              </div>\n\n              <button\n                onClick={handleSubmit}\n                className=\"w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600\"\n                disabled={uploading}\n              >\n                Xác nhận\n              </button>\n\n            </form>\n\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default InformationForm;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,UAAU,QAAQ,qCAAqC;AAChE,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,UAAU,QAAQ,wCAAwC;AACnE,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,eAAe,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,YAAY,CAAC,GAAGb,eAAe,CAAC,CAAC;EACxC,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,cAAc,CAAC,CAAC;EAEjC,MAAMc,MAAM,GAAGH,YAAY,CAACI,GAAG,CAAC,QAAQ,CAAC;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EACzD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAACc,YAAY,CAACI,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;EAE5E,MAAMW,aAAa,GAAG,iCAAiC;;EAEvD;EACApB,eAAe,CAAC,CAAC;;EAEjB;EACA,MAAMqB,kBAAkB,GAAG,MAAOC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IAC/E,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI,CAACC,IAAI,EAAE;IAEX,IAAI;MACFP,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMU,WAAW,GAAG,MAAM7B,UAAU,CAAC0B,IAAI,EAAE,aAAa,CAAC;MACzDI,OAAO,CAACC,GAAG,CAAC,yCAAyCF,WAAW,EAAE,CAAC;MAEnEZ,SAAS,CAAC,yCAAyCY,WAAW,EAAE,CAAC;MACjEV,YAAY,CAAC,KAAK,CAAC;MAEnBV,QAAQ,CAACV,QAAQ,CAAC;QAChBiC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,mBAAmB;QAC1BC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhB,YAAY,CAAC,KAAK,CAAC;MACnBW,OAAO,CAACK,KAAK,CAACA,KAAK,CAAC;MAEpB1B,QAAQ,CAACV,QAAQ,CAAC;QAChBiC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIxB,QAAQ,IAAIE,YAAY,IAAIJ,MAAM,IAAIM,MAAM,EAAE;MAChD,IAAI;QACF,MAAMqB,MAAM,GAAG,MAAMpC,WAAW,CAACqC,QAAQ,CAAC5B,MAAM,EAAE;UAChD6B,QAAQ,EAAE3B,QAAQ;UAClB4B,GAAG,EAAE1B,YAAY;UACjBE,MAAM,EAAEA;QACV,CAAC,EAAEI,QAAQ,IAAIqB,SAAS,CAAC;QAEzBX,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEM,MAAM,CAAC;QAE7B,MAAMK,aAAa,GAAGL,MAAM,CAACM,OAAO,CAACC,IAAI,CAAEC,MAAW,IAAKA,MAAM,CAACL,GAAG,KAAK1B,YAAY,CAAC;QACvFgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEW,aAAa,CAAC;;QAE3C;QACAI,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACP,aAAa,CAAC,CAAC;QACpEI,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACjC,MAAM,CAAC,CAAC;QAC7D8B,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAErC,MAAM,CAAC;QACtCoC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEjC,YAAY,CAAC;QAC9CgC,YAAY,CAACC,OAAO,CAAC,mBAAmB,EAAEnC,QAAQ,CAAC;;QAEnD;QACAH,QAAQ,CAACZ,UAAU,CAACwC,MAAM,CAACM,OAAO,CAAC,CAAC;QACpClC,QAAQ,CAACX,cAAc,CAAC;UACtBoD,EAAE,EAAExC,MAAM;UACVyC,IAAI,EAAEd,MAAM,CAACe,QAAQ,IAAI,WAAW;UACpCT,OAAO,EAAEN,MAAM,CAACM,OAAO;UACvBU,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;QAEH5C,QAAQ,CAACV,QAAQ,CAAC;UAChBiC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,qBAAqB;UAC5BC,OAAO,EAAE,aAAatB,QAAQ;QAChC,CAAC,CAAC,CAAC;QAEHJ,QAAQ,CAAC,wBAAwBE,MAAM,EAAE,CAAC;MAC5C,CAAC,CAAC,OAAOyB,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAACA,KAAK,CAAC;QAEpB1B,QAAQ,CAACV,QAAQ,CAAC;UAChBiC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,mBAAmB;UAC1BC,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;MACL;IACF,CAAC,MAAM;MACLzB,QAAQ,CAACV,QAAQ,CAAC;QAChBiC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,uBAAuB;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,oBACE9B,OAAA;IAAKkD,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBAEpDnD,OAAA;MAAKkD,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBAExFnD,OAAA;QAAKkD,SAAS,EAAC;MAAyM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE/NvD,OAAA;QAAKkD,SAAS,EAAC;MAAwF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE9GvD,OAAA;QAAKkD,SAAS,EAAC;MAAsH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzI,CAAC,eAGNvD,OAAA;MAAKkD,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EnD,OAAA;QAAKkD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BnD,OAAA;UAAKkD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BnD,OAAA;YAAIkD,SAAS,EAAC,6GAA6G;YAAAC,QAAA,EAAC;UAE5H;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvD,OAAA;YAAGkD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAExC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNvD,OAAA;UAAKkD,SAAS,EAAC,sFAAsF;UAAAC,QAAA,gBACnGnD,OAAA;YAAIkD,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELvD,OAAA;YAAMwD,QAAQ,EAAGC,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;YAAAP,QAAA,gBACxCnD,OAAA;cAAKkD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnD,OAAA;gBAAOkD,SAAS,EAAC,8CAA8C;gBAACS,OAAO,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvD,OAAA;gBAAKkD,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBnD,OAAA;kBACEkD,SAAS,EAAC,wMAAwM;kBAClNJ,EAAE,EAAC,OAAO;kBACVc,WAAW,EAAC,qCAAmB;kBAC/BC,KAAK,EAAErD,QAAS;kBAChBsD,QAAQ,EAAGL,CAAC,IAAKhD,WAAW,CAACgD,CAAC,CAAClC,MAAM,CAACsC,KAAK;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACFvD,OAAA;kBAAMkD,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,EAAC;gBAEvF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvD,OAAA;cAAKkD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnD,OAAA;gBAAOkD,SAAS,EAAC,8CAA8C;gBAACS,OAAO,EAAC,UAAU;gBAAAR,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvD,OAAA;gBAAKkD,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBnD,OAAA;kBACEkD,SAAS,EAAC,wMAAwM;kBAClNJ,EAAE,EAAC,UAAU;kBACbc,WAAW,EAAC,oCAAgB;kBAC5BC,KAAK,EAAEnD,YAAa;kBACpBoD,QAAQ,EAAGL,CAAC,IAAK9C,eAAe,CAAC8C,CAAC,CAAClC,MAAM,CAACsC,KAAK;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACFvD,OAAA;kBAAMkD,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,EAAC;gBAEvF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvD,OAAA;cAAKkD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnD,OAAA;gBAAOkD,SAAS,EAAC,8CAA8C;gBAACS,OAAO,EAAC,cAAc;gBAAAR,QAAA,EAAC;cAEvF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvD,OAAA;gBAAKkD,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBnD,OAAA;kBACEkD,SAAS,EAAC,wMAAwM;kBAClNtB,IAAI,EAAC,UAAU;kBACfkB,EAAE,EAAC,cAAc;kBACjBc,WAAW,EAAC,wDAA8B;kBAC1CC,KAAK,EAAE7C,QAAS;kBAChB8C,QAAQ,EAAGL,CAAC,IAAKxC,WAAW,CAACwC,CAAC,CAAClC,MAAM,CAACsC,KAAK;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACFvD,OAAA;kBAAMkD,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,EAAC;gBAEvF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNvD,OAAA;gBAAGkD,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENvD,OAAA;cAAKkD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BnD,OAAA;gBACE+D,GAAG,EAAEnD,MAAM,GAAG,GAAGA,MAAM,EAAE,GAAGM,aAAc;gBAC1C8C,GAAG,EAAC,QAAQ;gBACZd,SAAS,EAAC;cAAkD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACFvD,OAAA;gBAAOkD,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACrCnD,OAAA;kBAAMkD,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClDvD,OAAA;kBACE4B,IAAI,EAAC,MAAM;kBACXsB,SAAS,EAAC,QAAQ;kBAClBe,MAAM,EAAC,SAAS;kBAChBH,QAAQ,EAAE3C,kBAAmB;kBAC7B+C,QAAQ,EAAEpD;gBAAU;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACFvD,OAAA;kBACEkD,SAAS,EAAE,+CAA+CpC,SAAS,GAAG,gCAAgC,GAAG,+BAA+B,EACnI;kBAAAqC,QAAA,EAEJrC,SAAS,GAAG,aAAa,GAAG;gBAAkB;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENvD,OAAA;cACEmE,OAAO,EAAEnC,YAAa;cACtBkB,SAAS,EAAC,qEAAqE;cAC/EgB,QAAQ,EAAEpD,SAAU;cAAAqC,QAAA,EACrB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CAvOID,eAAe;EAAA,QACIX,eAAe,EACrBC,WAAW,EACXC,cAAc,EAY/BM,eAAe;AAAA;AAAAsE,EAAA,GAfXnE,eAAe;AAyOrB,eAAeA,eAAe;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}